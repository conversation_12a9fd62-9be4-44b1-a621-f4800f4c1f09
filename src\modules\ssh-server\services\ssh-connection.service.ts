import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NodeSSH } from 'node-ssh';
import { EncryptionService } from '@/shared/services/encryption.service';
import { 
  SshConnectionConfig, 
  SshCommandResult, 
  SshConnectionInfo,
  SshExecutionOptions,
  SshBatchCommand,
  SshBatchResult,
  SshHealthCheck,
} from '../interfaces/ssh-connection.interface';
import { SSH_DEFAULTS, SSH_ERROR_CODES, HEALTH_CHECK_COMMANDS } from '../constants/ssh-commands.constants';
import { SshServer } from '../entities/ssh-server.entity';

/**
 * Service quản lý kết nối SSH và thực thi lệnh từ xa
 */
@Injectable()
export class SshConnectionService {
  private readonly logger = new Logger(SshConnectionService.name);
  private readonly connectionPool = new Map<string, NodeSSH>();
  private readonly connectionInfo = new Map<string, SshConnectionInfo>();

  constructor(
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Tạo cấu hình kết nối từ SSH server entity
   * @param sshServer SSH server entity
   * @returns Cấu hình kết nối SSH
   */
  async createConnectionConfig(sshServer: SshServer): Promise<SshConnectionConfig> {
    const config: SshConnectionConfig = {
      host: sshServer.host,
      port: sshServer.port,
      username: sshServer.username,
      connectionTimeout: sshServer.connectionTimeout,
      commandTimeout: sshServer.commandTimeout,
      retryAttempts: sshServer.retryAttempts,
      retryDelay: sshServer.retryDelay,
    };

    // Giải mã password nếu có
    if (sshServer.passwordEncrypted) {
      try {
        config.password = await this.encryptionService.decrypt(sshServer.passwordEncrypted);
      } catch (error) {
        this.logger.error(`Lỗi giải mã password cho server ${sshServer.name}: ${error.message}`);
        throw new Error('Không thể giải mã password');
      }
    }

    // Giải mã private key nếu có
    if (sshServer.privateKeyEncrypted) {
      try {
        config.privateKey = await this.encryptionService.decrypt(sshServer.privateKeyEncrypted);
      } catch (error) {
        this.logger.error(`Lỗi giải mã private key cho server ${sshServer.name}: ${error.message}`);
        throw new Error('Không thể giải mã private key');
      }
    }

    // Giải mã passphrase nếu có
    if (sshServer.passphraseEncrypted) {
      try {
        config.passphrase = await this.encryptionService.decrypt(sshServer.passphraseEncrypted);
      } catch (error) {
        this.logger.error(`Lỗi giải mã passphrase cho server ${sshServer.name}: ${error.message}`);
        throw new Error('Không thể giải mã passphrase');
      }
    }

    return config;
  }

  /**
   * Tạo kết nối SSH mới
   * @param config Cấu hình kết nối
   * @returns Connection ID
   */
  async createConnection(config: SshConnectionConfig): Promise<string> {
    const connectionId = this.generateConnectionId(config);
    
    // Kiểm tra kết nối đã tồn tại
    if (this.connectionPool.has(connectionId)) {
      const existingConnection = this.connectionPool.get(connectionId)!;
      if (existingConnection.isConnected()) {
        this.logger.debug(`Sử dụng kết nối SSH hiện có: ${connectionId}`);
        return connectionId;
      } else {
        // Xóa kết nối cũ không hoạt động
        this.connectionPool.delete(connectionId);
        this.connectionInfo.delete(connectionId);
      }
    }

    const ssh = new NodeSSH();
    const connectionInfo: SshConnectionInfo = {
      connectionId,
      server: {
        host: config.host,
        port: config.port,
        username: config.username,
      },
      status: 'connecting',
    };

    this.connectionInfo.set(connectionId, connectionInfo);

    try {
      this.logger.log(`Đang kết nối SSH tới ${config.host}:${config.port} với user ${config.username}`);

      const connectOptions: any = {
        host: config.host,
        port: config.port,
        username: config.username,
        readyTimeout: config.connectionTimeout || SSH_DEFAULTS.CONNECTION_TIMEOUT,
      };

      // Sử dụng password hoặc private key
      if (config.password) {
        connectOptions.password = config.password;
      } else if (config.privateKey) {
        connectOptions.privateKey = config.privateKey;
        if (config.passphrase) {
          connectOptions.passphrase = config.passphrase;
        }
      } else {
        throw new Error('Cần cung cấp password hoặc private key');
      }

      await ssh.connect(connectOptions);

      // Cập nhật thông tin kết nối
      connectionInfo.status = 'connected';
      connectionInfo.connectedAt = Date.now();
      this.connectionPool.set(connectionId, ssh);

      this.logger.log(`Kết nối SSH thành công: ${connectionId}`);
      return connectionId;

    } catch (error) {
      connectionInfo.status = 'error';
      connectionInfo.error = error.message;
      
      this.logger.error(`Lỗi kết nối SSH tới ${config.host}:${config.port}: ${error.message}`);
      
      // Xóa thông tin kết nối lỗi
      this.connectionInfo.delete(connectionId);
      
      throw new Error(`${SSH_ERROR_CODES.CONNECTION_FAILED}: ${error.message}`);
    }
  }

  /**
   * Thực thi lệnh SSH
   * @param connectionId ID kết nối
   * @param command Lệnh cần thực thi
   * @param options Tùy chọn thực thi
   * @returns Kết quả thực thi
   */
  async executeCommand(
    connectionId: string,
    command: string,
    options: SshExecutionOptions = {},
  ): Promise<SshCommandResult> {
    const ssh = this.connectionPool.get(connectionId);
    if (!ssh || !ssh.isConnected()) {
      throw new Error(`${SSH_ERROR_CODES.CONNECTION_LOST}: Kết nối SSH không tồn tại hoặc đã bị ngắt`);
    }

    const startTime = Date.now();
    this.logger.debug(`Thực thi lệnh SSH: ${command}`);

    try {
      const execOptions: any = {
        cwd: options.workingDirectory,
        env: options.environment,
      };

      // Thực thi lệnh với timeout
      const result = await Promise.race([
        ssh.execCommand(command, execOptions),
        this.createTimeoutPromise(options.timeout || SSH_DEFAULTS.COMMAND_TIMEOUT),
      ]);

      const executionTime = Date.now() - startTime;
      const success = result.code === 0;

      const commandResult: SshCommandResult = {
        command,
        stdout: result.stdout,
        stderr: result.stderr,
        exitCode: result.code,
        executionTime,
        success,
      };

      if (!success) {
        commandResult.error = result.stderr || `Command failed with exit code ${result.code}`;
        this.logger.warn(`Lệnh SSH thất bại: ${command} (Exit code: ${result.code})`);
      } else {
        this.logger.debug(`Lệnh SSH thành công: ${command} (${executionTime}ms)`);
      }

      return commandResult;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.logger.error(`Lỗi thực thi lệnh SSH: ${command} - ${error.message}`);
      
      return {
        command,
        stdout: '',
        stderr: error.message,
        exitCode: -1,
        executionTime,
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Thực thi batch commands
   * @param connectionId ID kết nối
   * @param commands Danh sách lệnh
   * @returns Kết quả batch execution
   */
  async executeBatchCommands(
    connectionId: string,
    commands: SshBatchCommand[],
  ): Promise<SshBatchResult> {
    const startTime = Date.now();
    const results: Array<SshCommandResult & { description?: string }> = [];
    let successCount = 0;
    let failureCount = 0;

    this.logger.log(`Bắt đầu thực thi batch commands (${commands.length} lệnh)`);

    for (const batchCommand of commands) {
      try {
        const result = await this.executeCommand(connectionId, batchCommand.command, {
          workingDirectory: batchCommand.workingDirectory,
          environment: batchCommand.environment,
        });

        results.push({
          ...result,
          description: batchCommand.description,
        });

        if (result.success) {
          successCount++;
        } else {
          failureCount++;
          
          // Dừng nếu lệnh thất bại và không được cấu hình tiếp tục
          if (!batchCommand.continueOnError) {
            this.logger.warn(`Dừng batch execution do lệnh thất bại: ${batchCommand.command}`);
            break;
          }
        }

      } catch (error) {
        failureCount++;
        results.push({
          command: batchCommand.command,
          stdout: '',
          stderr: error.message,
          exitCode: -1,
          executionTime: 0,
          success: false,
          error: error.message,
          description: batchCommand.description,
        });

        if (!batchCommand.continueOnError) {
          break;
        }
      }
    }

    const totalExecutionTime = Date.now() - startTime;
    const overallSuccess = failureCount === 0;

    this.logger.log(`Hoàn thành batch execution: ${successCount}/${commands.length} thành công (${totalExecutionTime}ms)`);

    return {
      totalCommands: commands.length,
      successCount,
      failureCount,
      results,
      totalExecutionTime,
      overallSuccess,
    };
  }

  /**
   * Kiểm tra sức khỏe server SSH
   * @param connectionId ID kết nối
   * @returns Thông tin health check
   */
  async healthCheck(connectionId: string): Promise<SshHealthCheck> {
    const startTime = Date.now();
    
    try {
      const commands = HEALTH_CHECK_COMMANDS.BASIC;
      const results = await this.executeBatchCommands(connectionId, commands);
      
      const responseTime = Date.now() - startTime;
      const systemInfo: any = {};

      // Parse kết quả health check
      for (const result of results.results) {
        if (result.success) {
          if (result.description?.includes('uptime')) {
            systemInfo.uptime = result.stdout.trim();
          } else if (result.description?.includes('Memory')) {
            systemInfo.memoryUsage = result.stdout.trim();
          } else if (result.description?.includes('Disk')) {
            systemInfo.diskUsage = result.stdout.trim();
          }
        }
      }

      return {
        connectionStatus: results.overallSuccess ? 'healthy' : 'unhealthy',
        responseTime,
        systemInfo,
        checkedAt: Date.now(),
      };

    } catch (error) {
      return {
        connectionStatus: 'unhealthy',
        responseTime: Date.now() - startTime,
        checkedAt: Date.now(),
        error: error.message,
      };
    }
  }

  /**
   * Đóng kết nối SSH
   * @param connectionId ID kết nối
   */
  async closeConnection(connectionId: string): Promise<void> {
    const ssh = this.connectionPool.get(connectionId);
    const connectionInfo = this.connectionInfo.get(connectionId);

    if (ssh) {
      try {
        ssh.dispose();
        this.logger.debug(`Đã đóng kết nối SSH: ${connectionId}`);
      } catch (error) {
        this.logger.error(`Lỗi đóng kết nối SSH: ${error.message}`);
      }
    }

    if (connectionInfo) {
      connectionInfo.status = 'disconnected';
      connectionInfo.disconnectedAt = Date.now();
    }

    this.connectionPool.delete(connectionId);
    this.connectionInfo.delete(connectionId);
  }

  /**
   * Lấy thông tin kết nối
   * @param connectionId ID kết nối
   * @returns Thông tin kết nối
   */
  getConnectionInfo(connectionId: string): SshConnectionInfo | undefined {
    return this.connectionInfo.get(connectionId);
  }

  /**
   * Lấy danh sách tất cả kết nối
   * @returns Danh sách thông tin kết nối
   */
  getAllConnections(): SshConnectionInfo[] {
    return Array.from(this.connectionInfo.values());
  }

  /**
   * Đóng tất cả kết nối
   */
  async closeAllConnections(): Promise<void> {
    const connectionIds = Array.from(this.connectionPool.keys());
    
    await Promise.all(
      connectionIds.map(connectionId => this.closeConnection(connectionId))
    );

    this.logger.log(`Đã đóng tất cả ${connectionIds.length} kết nối SSH`);
  }

  /**
   * Tạo connection ID duy nhất
   * @param config Cấu hình kết nối
   * @returns Connection ID
   */
  private generateConnectionId(config: SshConnectionConfig): string {
    return `ssh_${config.host}_${config.port}_${config.username}_${Date.now()}`;
  }

  /**
   * Tạo promise timeout
   * @param timeout Thời gian timeout (ms)
   * @returns Promise timeout
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`${SSH_ERROR_CODES.COMMAND_TIMEOUT}: Command timeout after ${timeout}ms`));
      }, timeout);
    });
  }
}
