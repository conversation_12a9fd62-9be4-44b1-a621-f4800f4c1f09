import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO response cho SSH server
 */
export class SshServerResponseDto {
  /**
   * ID server SSH
   */
  @ApiProperty({
    description: 'ID server SSH',
    example: 1,
  })
  id: number;

  /**
   * Tên server SSH
   */
  @ApiProperty({
    description: 'Tên server SSH',
    example: 'Production Server 1',
  })
  name: string;

  /**
   * Địa chỉ host
   */
  @ApiProperty({
    description: 'Địa chỉ host',
    example: '*************',
  })
  host: string;

  /**
   * Port SSH
   */
  @ApiProperty({
    description: 'Port SSH',
    example: 22,
  })
  port: number;

  /**
   * Username
   */
  @ApiProperty({
    description: 'Username',
    example: 'ubuntu',
  })
  username: string;

  /**
   * Mô tả server
   */
  @ApiPropertyOptional({
    description: 'Mô tả server',
    example: 'Production server for web application',
  })
  description?: string;

  /**
   * Trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động',
    example: true,
  })
  isActive: boolean;

  /**
   * Timeout kết nối (ms)
   */
  @ApiProperty({
    description: 'Timeout kết nối (ms)',
    example: 30000,
  })
  connectionTimeout: number;

  /**
   * Timeout lệnh (ms)
   */
  @ApiProperty({
    description: 'Timeout lệnh (ms)',
    example: 300000,
  })
  commandTimeout: number;

  /**
   * Số lần thử lại
   */
  @ApiProperty({
    description: 'Số lần thử lại',
    example: 3,
  })
  retryAttempts: number;

  /**
   * Delay thử lại (ms)
   */
  @ApiProperty({
    description: 'Delay thử lại (ms)',
    example: 5000,
  })
  retryDelay: number;

  /**
   * Thời gian kết nối cuối
   */
  @ApiPropertyOptional({
    description: 'Thời gian kết nối cuối',
    example: 1640995200000,
  })
  lastConnectedAt?: number;

  /**
   * Trạng thái kết nối cuối
   */
  @ApiPropertyOptional({
    description: 'Trạng thái kết nối cuối',
    example: 'connected',
  })
  lastConnectionStatus?: string;

  /**
   * Lỗi kết nối cuối
   */
  @ApiPropertyOptional({
    description: 'Lỗi kết nối cuối',
    example: null,
  })
  lastConnectionError?: string;

  /**
   * ID người tạo
   */
  @ApiProperty({
    description: 'ID người tạo',
    example: 1,
  })
  createdBy: number;

  /**
   * ID người cập nhật cuối
   */
  @ApiPropertyOptional({
    description: 'ID người cập nhật cuối',
    example: 1,
  })
  updatedBy?: number;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1640995200000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối
   */
  @ApiProperty({
    description: 'Thời gian cập nhật cuối',
    example: 1640995200000,
  })
  updatedAt: number;
}

/**
 * DTO response cho danh sách SSH servers với phân trang
 */
export class SshServerListResponseDto {
  /**
   * Danh sách SSH servers
   */
  @ApiProperty({
    description: 'Danh sách SSH servers',
    type: [SshServerResponseDto],
  })
  items: SshServerResponseDto[];

  /**
   * Tổng số SSH servers
   */
  @ApiProperty({
    description: 'Tổng số SSH servers',
    example: 10,
  })
  total: number;

  /**
   * Trang hiện tại
   */
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  page: number;

  /**
   * Số items per page
   */
  @ApiProperty({
    description: 'Số items per page',
    example: 10,
  })
  limit: number;

  /**
   * Tổng số trang
   */
  @ApiProperty({
    description: 'Tổng số trang',
    example: 1,
  })
  totalPages: number;
}

/**
 * DTO response cho test kết nối SSH
 */
export class SshConnectionTestResponseDto {
  /**
   * Kết nối thành công
   */
  @ApiProperty({
    description: 'Kết nối thành công',
    example: true,
  })
  success: boolean;

  /**
   * Thời gian response (ms)
   */
  @ApiProperty({
    description: 'Thời gian response (ms)',
    example: 1500,
  })
  responseTime: number;

  /**
   * Thông báo
   */
  @ApiProperty({
    description: 'Thông báo',
    example: 'Kết nối SSH thành công',
  })
  message: string;

  /**
   * Thông báo lỗi (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: null,
  })
  error?: string;

  /**
   * Thông tin hệ thống (nếu kết nối thành công)
   */
  @ApiPropertyOptional({
    description: 'Thông tin hệ thống (nếu kết nối thành công)',
    example: {
      uptime: '1 day, 2 hours',
      memoryUsage: '2.1G/8.0G',
      diskUsage: '45% used',
    },
  })
  systemInfo?: {
    uptime?: string;
    memoryUsage?: string;
    diskUsage?: string;
    loadAverage?: string;
  };

  /**
   * Thời gian test
   */
  @ApiProperty({
    description: 'Thời gian test',
    example: 1640995200000,
  })
  testedAt: number;
}

/**
 * DTO response cho thống kê SSH servers
 */
export class SshServerStatisticsResponseDto {
  /**
   * Tổng số servers
   */
  @ApiProperty({
    description: 'Tổng số servers',
    example: 10,
  })
  total: number;

  /**
   * Số servers đang hoạt động
   */
  @ApiProperty({
    description: 'Số servers đang hoạt động',
    example: 8,
  })
  active: number;

  /**
   * Số servers không hoạt động
   */
  @ApiProperty({
    description: 'Số servers không hoạt động',
    example: 2,
  })
  inactive: number;

  /**
   * Số servers đã kết nối
   */
  @ApiProperty({
    description: 'Số servers đã kết nối',
    example: 6,
  })
  connected: number;

  /**
   * Số servers chưa kết nối
   */
  @ApiProperty({
    description: 'Số servers chưa kết nối',
    example: 2,
  })
  disconnected: number;
}
