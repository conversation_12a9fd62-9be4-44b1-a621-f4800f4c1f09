import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';

/**
 * Entity đại diện cho log thực thi lệnh SSH
 * Lưu trữ lịch sử các lệnh đã thực thi trên server SSH
 */
@Entity('ssh_execution_logs')
@Index(['tenantId'])
@Index(['sshServerId'])
@Index(['executedBy'])
@Index(['executedAt'])
@Index(['tenantId', 'sshServerId'])
@Index(['tenantId', 'executedAt'])
@Index(['status'])
export class SshExecutionLog {
  /**
   * ID duy nhất của log thực thi
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID tenant để phân tách dữ liệu
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;

  /**
   * ID server SSH
   */
  @Column({ name: 'ssh_server_id', type: 'integer', nullable: false })
  sshServerId: number;

  /**
   * L<PERSON><PERSON> đã thực thi
   */
  @Column({ name: 'command', type: 'text', nullable: false })
  command: string;

  /**
   * Thư mục làm việc khi thực thi lệnh
   */
  @Column({ name: 'working_directory', type: 'varchar', length: 500, nullable: true })
  workingDirectory: string | null;

  /**
   * Biến môi trường (JSON string)
   */
  @Column({ name: 'environment_variables', type: 'text', nullable: true })
  environmentVariables: string | null;

  /**
   * Output của lệnh
   */
  @Column({ name: 'output', type: 'text', nullable: true })
  output: string | null;

  /**
   * Error output của lệnh
   */
  @Column({ name: 'error_output', type: 'text', nullable: true })
  errorOutput: string | null;

  /**
   * Exit code của lệnh
   */
  @Column({ name: 'exit_code', type: 'integer', nullable: true })
  exitCode: number | null;

  /**
   * Thời gian thực thi (milliseconds)
   */
  @Column({ name: 'execution_time_ms', type: 'integer', nullable: true })
  executionTimeMs: number | null;

  /**
   * Trạng thái thực thi
   */
  @Column({ 
    name: 'status', 
    type: 'varchar', 
    length: 50, 
    default: 'pending',
    nullable: false 
  })
  status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout' | 'cancelled';

  /**
   * ID job queue (nếu thực thi qua queue)
   */
  @Column({ name: 'job_id', type: 'varchar', length: 255, nullable: true })
  jobId: string | null;

  /**
   * Loại thực thi
   */
  @Column({ 
    name: 'execution_type', 
    type: 'varchar', 
    length: 50, 
    default: 'manual',
    nullable: false 
  })
  executionType: 'manual' | 'scheduled' | 'deployment' | 'health_check' | 'batch';

  /**
   * Metadata bổ sung (JSON string)
   */
  @Column({ name: 'metadata', type: 'text', nullable: true })
  metadata: string | null;

  /**
   * ID người thực thi
   */
  @Column({ name: 'executed_by', type: 'integer', nullable: false })
  executedBy: number;

  /**
   * Thời gian bắt đầu thực thi (timestamp)
   */
  @Column({ name: 'executed_at', type: 'bigint', nullable: false })
  executedAt: number;

  /**
   * Thời gian hoàn thành (timestamp)
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  /**
   * Thời gian tạo log (timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;
}
