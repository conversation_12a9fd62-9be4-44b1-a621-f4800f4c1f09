import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, Min } from 'class-validator';

/**
 * DTO cho cập nhật người được giao việc của công việc
 */
export class UpdateTodoAssigneeDto {
  /**
   * ID người được giao việc mới
   * @example 5
   */
  @ApiProperty({
    description: 'ID người được giao việc mới (null để bỏ giao việc)',
    example: 5,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsInt({ message: 'ID người được giao việc phải là số nguyên' })
  @Min(1, { message: 'ID người được giao việc phải lớn hơn 0' })
  assigneeId?: number | null;
}
