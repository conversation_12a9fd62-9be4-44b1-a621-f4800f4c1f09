import { Test, TestingModule } from '@nestjs/testing';
import { SshCommandService } from '../services/ssh-command.service';
import { SshConnectionService } from '../services/ssh-connection.service';
import { SshExecutionLogRepository } from '../repositories/ssh-execution-log.repository';
import { SshServerRepository } from '../repositories/ssh-server.repository';

describe('SshCommandService', () => {
  let service: SshCommandService;
  let connectionService: jest.Mocked<SshConnectionService>;
  let executionLogRepository: jest.Mocked<SshExecutionLogRepository>;
  let serverRepository: jest.Mocked<SshServerRepository>;

  const mockSshServer = {
    id: 1,
    name: 'Test Server',
    host: '*************',
    port: 22,
    username: 'ubuntu',
    isActive: true,
    connectionTimeout: 30000,
    commandTimeout: 300000,
    retryAttempts: 3,
    retryDelay: 5000,
  };

  const mockExecutionLog = {
    id: 1,
    sshServerId: 1,
    command: 'ls -la',
    status: 'pending',
    executionType: 'manual',
    executedBy: 1,
    executedAt: Date.now(),
    createdAt: Date.now(),
  };

  const mockCommandResult = {
    command: 'ls -la',
    stdout: 'total 8\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 .',
    stderr: '',
    exitCode: 0,
    executionTime: 1500,
    success: true,
  };

  beforeEach(async () => {
    const mockConnectionService = {
      createConnectionConfig: jest.fn(),
      createConnection: jest.fn(),
      executeCommand: jest.fn(),
      closeConnection: jest.fn(),
    };

    const mockExecutionLogRepo = {
      create: jest.fn(),
      updateStatus: jest.fn(),
      findById: jest.fn(),
      findWithPagination: jest.fn(),
    };

    const mockServerRepo = {
      findById: jest.fn(),
      updateConnectionStatus: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SshCommandService,
        {
          provide: SshConnectionService,
          useValue: mockConnectionService,
        },
        {
          provide: SshExecutionLogRepository,
          useValue: mockExecutionLogRepo,
        },
        {
          provide: SshServerRepository,
          useValue: mockServerRepo,
        },
      ],
    }).compile();

    service = module.get<SshCommandService>(SshCommandService);
    connectionService = module.get(SshConnectionService);
    executionLogRepository = module.get(SshExecutionLogRepository);
    serverRepository = module.get(SshServerRepository);
  });

  describe('executeCommand', () => {
    it('should execute command successfully', async () => {
      serverRepository.findById.mockResolvedValue(mockSshServer);
      executionLogRepository.create.mockResolvedValue(mockExecutionLog);
      connectionService.createConnectionConfig.mockResolvedValue({
        host: '*************',
        port: 22,
        username: 'ubuntu',
        password: 'password',
      });
      connectionService.createConnection.mockResolvedValue('connection-id');
      connectionService.executeCommand.mockResolvedValue(mockCommandResult);
      executionLogRepository.updateStatus.mockResolvedValue(undefined);
      serverRepository.updateConnectionStatus.mockResolvedValue(undefined);

      const result = await service.executeCommand(1, 'ls -la', 1);

      expect(result.result.success).toBe(true);
      expect(result.result.command).toBe('ls -la');
      expect(result.result.exitCode).toBe(0);
      expect(result.logId).toBe(1);

      expect(serverRepository.findById).toHaveBeenCalledWith(1);
      expect(executionLogRepository.create).toHaveBeenCalled();
      expect(connectionService.createConnection).toHaveBeenCalled();
      expect(connectionService.executeCommand).toHaveBeenCalledWith(
        'connection-id',
        'ls -la',
        expect.any(Object)
      );
      expect(executionLogRepository.updateStatus).toHaveBeenCalledWith(
        1,
        'completed',
        mockCommandResult.stdout,
        mockCommandResult.stderr,
        mockCommandResult.exitCode,
        mockCommandResult.executionTime
      );
      expect(connectionService.closeConnection).toHaveBeenCalledWith('connection-id');
    });

    it('should handle server not found', async () => {
      serverRepository.findById.mockResolvedValue(null);

      await expect(service.executeCommand(999, 'ls -la', 1)).rejects.toThrow(
        'SSH server với ID 999 không tồn tại'
      );
    });

    it('should handle inactive server', async () => {
      const inactiveServer = { ...mockSshServer, isActive: false };
      serverRepository.findById.mockResolvedValue(inactiveServer);

      await expect(service.executeCommand(1, 'ls -la', 1)).rejects.toThrow(
        'SSH server Test Server đã bị vô hiệu hóa'
      );
    });

    it('should handle command execution failure', async () => {
      const failedResult = {
        ...mockCommandResult,
        success: false,
        exitCode: 1,
        stderr: 'Command failed',
        error: 'Command execution failed',
      };

      serverRepository.findById.mockResolvedValue(mockSshServer);
      executionLogRepository.create.mockResolvedValue(mockExecutionLog);
      connectionService.createConnectionConfig.mockResolvedValue({});
      connectionService.createConnection.mockResolvedValue('connection-id');
      connectionService.executeCommand.mockResolvedValue(failedResult);

      const result = await service.executeCommand(1, 'invalid-command', 1);

      expect(result.result.success).toBe(false);
      expect(result.result.exitCode).toBe(1);
      expect(result.result.error).toBe('Command execution failed');

      expect(executionLogRepository.updateStatus).toHaveBeenCalledWith(
        1,
        'failed',
        failedResult.stdout,
        failedResult.stderr,
        failedResult.exitCode,
        failedResult.executionTime
      );
    });

    it('should handle connection error', async () => {
      serverRepository.findById.mockResolvedValue(mockSshServer);
      executionLogRepository.create.mockResolvedValue(mockExecutionLog);
      connectionService.createConnectionConfig.mockResolvedValue({});
      connectionService.createConnection.mockRejectedValue(new Error('Connection failed'));

      const result = await service.executeCommand(1, 'ls -la', 1);

      expect(result.result.success).toBe(false);
      expect(result.result.error).toBe('Connection failed');

      expect(executionLogRepository.updateStatus).toHaveBeenCalledWith(
        1,
        'failed',
        '',
        'Connection failed',
        -1
      );
      expect(serverRepository.updateConnectionStatus).toHaveBeenCalledWith(
        1,
        'error',
        'Connection failed'
      );
    });
  });

  describe('executeBatchCommands', () => {
    const batchCommands = [
      {
        command: 'echo "Command 1"',
        description: 'First command',
      },
      {
        command: 'echo "Command 2"',
        description: 'Second command',
      },
    ];

    it('should execute batch commands successfully', async () => {
      const batchResults = {
        totalCommands: 2,
        successCount: 2,
        failureCount: 0,
        results: [
          { ...mockCommandResult, command: 'echo "Command 1"' },
          { ...mockCommandResult, command: 'echo "Command 2"' },
        ],
        totalExecutionTime: 3000,
        overallSuccess: true,
      };

      serverRepository.findById.mockResolvedValue(mockSshServer);
      connectionService.createConnectionConfig.mockResolvedValue({});
      connectionService.createConnection.mockResolvedValue('connection-id');
      
      // Mock individual command executions
      executionLogRepository.create
        .mockResolvedValueOnce({ ...mockExecutionLog, id: 1 })
        .mockResolvedValueOnce({ ...mockExecutionLog, id: 2 });

      const result = await service.executeBatchCommands(1, batchCommands, 1);

      expect(result.result.overallSuccess).toBe(true);
      expect(result.result.totalCommands).toBe(2);
      expect(result.result.successCount).toBe(2);
      expect(result.logIds).toHaveLength(2);

      expect(connectionService.closeConnection).toHaveBeenCalledWith('connection-id');
      expect(serverRepository.updateConnectionStatus).toHaveBeenCalledWith(
        1,
        'connected',
        undefined
      );
    });

    it('should handle partial batch failure', async () => {
      const mixedResults = [
        { ...mockCommandResult, command: 'echo "Command 1"', success: true },
        { 
          ...mockCommandResult, 
          command: 'invalid-command', 
          success: false, 
          exitCode: 1,
          error: 'Command not found'
        },
      ];

      serverRepository.findById.mockResolvedValue(mockSshServer);
      connectionService.createConnectionConfig.mockResolvedValue({});
      connectionService.createConnection.mockResolvedValue('connection-id');
      
      executionLogRepository.create
        .mockResolvedValueOnce({ ...mockExecutionLog, id: 1 })
        .mockResolvedValueOnce({ ...mockExecutionLog, id: 2 });

      const result = await service.executeBatchCommands(1, batchCommands, 1);

      expect(result.result.overallSuccess).toBe(false);
      expect(result.result.successCount).toBe(1);
      expect(result.result.failureCount).toBe(1);

      expect(serverRepository.updateConnectionStatus).toHaveBeenCalledWith(
        1,
        'error',
        'Một số lệnh thất bại trong batch execution'
      );
    });
  });

  describe('getCommonCommands', () => {
    it('should return system commands', () => {
      const commands = service.getCommonCommands('SYSTEM');
      
      expect(commands).toHaveProperty('UPTIME');
      expect(commands).toHaveProperty('FREE');
      expect(commands).toHaveProperty('DF');
      expect(commands).toHaveProperty('PS');
    });

    it('should return git commands', () => {
      const commands = service.getCommonCommands('GIT');
      
      expect(commands).toHaveProperty('STATUS');
      expect(commands).toHaveProperty('PULL');
      expect(commands).toHaveProperty('CLONE');
      expect(commands).toHaveProperty('CHECKOUT');
    });
  });

  describe('getDeploymentTemplates', () => {
    it('should return all deployment templates', () => {
      const templates = service.getDeploymentTemplates();
      
      expect(templates).toHaveProperty('NODEJS');
      expect(templates).toHaveProperty('REACT');
      expect(templates).toHaveProperty('DOCKER');
      expect(templates).toHaveProperty('NESTJS');
      expect(templates).toHaveProperty('CUSTOM');

      expect(templates.NODEJS).toHaveProperty('name');
      expect(templates.NODEJS).toHaveProperty('description');
      expect(templates.NODEJS).toHaveProperty('commands');
      expect(templates.NODEJS.commands).toBeInstanceOf(Array);
    });
  });
});
