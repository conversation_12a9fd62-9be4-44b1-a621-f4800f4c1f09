import { Test, TestingModule } from '@nestjs/testing';
import { SshServerService } from '../services/ssh-server.service';
import { SshServerRepository } from '../repositories/ssh-server.repository';
import { SshConnectionService } from '../services/ssh-connection.service';
import { EncryptionService } from '@/shared/services/encryption.service';
import { AppException, ErrorCode } from '@/common';
import { CreateSshServerDto } from '../dtos/requests/create-ssh-server.dto';

describe('SshServerService', () => {
  let service: SshServerService;
  let repository: jest.Mocked<SshServerRepository>;
  let connectionService: jest.Mocked<SshConnectionService>;
  let encryptionService: jest.Mocked<EncryptionService>;

  const mockSshServer = {
    id: 1,
    name: 'Test Server',
    host: '*************',
    port: 22,
    username: 'ubuntu',
    passwordEncrypted: 'encrypted-password',
    description: 'Test SSH server',
    isActive: true,
    connectionTimeout: 30000,
    commandTimeout: 300000,
    retryAttempts: 3,
    retryDelay: 5000,
    createdBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  beforeEach(async () => {
    const mockRepository = {
      findByName: jest.fn(),
      create: jest.fn(),
      findById: jest.fn(),
      findWithPagination: jest.fn(),
      findAllActive: jest.fn(),
      update: jest.fn(),
      softDelete: jest.fn(),
      hardDelete: jest.fn(),
      updateConnectionStatus: jest.fn(),
      getStatistics: jest.fn(),
    };

    const mockConnectionService = {
      createConnectionConfig: jest.fn(),
      createConnection: jest.fn(),
      healthCheck: jest.fn(),
      closeConnection: jest.fn(),
    };

    const mockEncryptionService = {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SshServerService,
        {
          provide: SshServerRepository,
          useValue: mockRepository,
        },
        {
          provide: SshConnectionService,
          useValue: mockConnectionService,
        },
        {
          provide: EncryptionService,
          useValue: mockEncryptionService,
        },
      ],
    }).compile();

    service = module.get<SshServerService>(SshServerService);
    repository = module.get(SshServerRepository);
    connectionService = module.get(SshConnectionService);
    encryptionService = module.get(EncryptionService);
  });

  describe('create', () => {
    const createDto: CreateSshServerDto = {
      name: 'Test Server',
      host: '*************',
      port: 22,
      username: 'ubuntu',
      password: 'plain-password',
      description: 'Test SSH server',
    };

    it('should create SSH server successfully', async () => {
      repository.findByName.mockResolvedValue(null);
      encryptionService.encrypt.mockResolvedValue('encrypted-password');
      repository.create.mockResolvedValue(mockSshServer);

      const result = await service.create(createDto, 1);

      expect(repository.findByName).toHaveBeenCalledWith('Test Server');
      expect(encryptionService.encrypt).toHaveBeenCalledWith('plain-password');
      expect(repository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Server',
          host: '*************',
          passwordEncrypted: 'encrypted-password',
          createdBy: 1,
        })
      );
      expect(result).toEqual(mockSshServer);
    });

    it('should throw error if server name already exists', async () => {
      repository.findByName.mockResolvedValue(mockSshServer);

      await expect(service.create(createDto, 1)).rejects.toThrow(AppException);
      expect(repository.findByName).toHaveBeenCalledWith('Test Server');
      expect(repository.create).not.toHaveBeenCalled();
    });

    it('should throw error if no password or private key provided', async () => {
      const invalidDto = { ...createDto };
      delete invalidDto.password;

      repository.findByName.mockResolvedValue(null);

      await expect(service.create(invalidDto, 1)).rejects.toThrow(AppException);
    });
  });

  describe('findById', () => {
    it('should return SSH server if found', async () => {
      repository.findById.mockResolvedValue(mockSshServer);

      const result = await service.findById(1);

      expect(repository.findById).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockSshServer);
    });

    it('should return null if not found', async () => {
      repository.findById.mockResolvedValue(null);

      const result = await service.findById(999);

      expect(repository.findById).toHaveBeenCalledWith(999);
      expect(result).toBeNull();
    });
  });

  describe('findByIdOrThrow', () => {
    it('should return SSH server if found', async () => {
      repository.findById.mockResolvedValue(mockSshServer);

      const result = await service.findByIdOrThrow(1);

      expect(result).toEqual(mockSshServer);
    });

    it('should throw error if not found', async () => {
      repository.findById.mockResolvedValue(null);

      await expect(service.findByIdOrThrow(999)).rejects.toThrow(AppException);
    });
  });

  describe('testConnection', () => {
    it('should test connection successfully', async () => {
      repository.findById.mockResolvedValue(mockSshServer);
      connectionService.createConnectionConfig.mockResolvedValue({
        host: '*************',
        port: 22,
        username: 'ubuntu',
        password: 'decrypted-password',
      });
      connectionService.createConnection.mockResolvedValue('connection-id');
      connectionService.healthCheck.mockResolvedValue({
        connectionStatus: 'healthy',
        responseTime: 1500,
        systemInfo: { uptime: '1 day' },
        checkedAt: Date.now(),
      });

      const result = await service.testConnection(1);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Kết nối SSH thành công');
      expect(result.systemInfo).toEqual({ uptime: '1 day' });
      expect(connectionService.closeConnection).toHaveBeenCalledWith('connection-id');
      expect(repository.updateConnectionStatus).toHaveBeenCalledWith(1, 'connected');
    });

    it('should handle connection failure', async () => {
      repository.findById.mockResolvedValue(mockSshServer);
      connectionService.createConnectionConfig.mockResolvedValue({
        host: '*************',
        port: 22,
        username: 'ubuntu',
        password: 'decrypted-password',
      });
      connectionService.createConnection.mockRejectedValue(new Error('Connection failed'));

      const result = await service.testConnection(1);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Kết nối SSH thất bại');
      expect(result.error).toBe('Connection failed');
      expect(repository.updateConnectionStatus).toHaveBeenCalledWith(1, 'error', 'Connection failed');
    });

    it('should return error for inactive server', async () => {
      const inactiveServer = { ...mockSshServer, isActive: false };
      repository.findById.mockResolvedValue(inactiveServer);

      const result = await service.testConnection(1);

      expect(result.success).toBe(false);
      expect(result.message).toBe('SSH server đã bị vô hiệu hóa');
      expect(result.error).toBe('Server is inactive');
    });
  });

  describe('update', () => {
    const updateDto = {
      name: 'Updated Server',
      description: 'Updated description',
    };

    it('should update SSH server successfully', async () => {
      const updatedServer = { ...mockSshServer, ...updateDto };
      repository.findById.mockResolvedValue(mockSshServer);
      repository.findByName.mockResolvedValue(null);
      repository.update.mockResolvedValue(updatedServer);

      const result = await service.update(1, updateDto, 1);

      expect(repository.update).toHaveBeenCalledWith(1, expect.objectContaining({
        name: 'Updated Server',
        description: 'Updated description',
        updatedBy: 1,
      }));
      expect(result).toEqual(updatedServer);
    });

    it('should throw error if new name already exists', async () => {
      const anotherServer = { ...mockSshServer, id: 2, name: 'Updated Server' };
      repository.findById.mockResolvedValue(mockSshServer);
      repository.findByName.mockResolvedValue(anotherServer);

      await expect(service.update(1, updateDto, 1)).rejects.toThrow(AppException);
    });
  });

  describe('softDelete', () => {
    it('should soft delete SSH server successfully', async () => {
      repository.findById.mockResolvedValue(mockSshServer);
      repository.softDelete.mockResolvedValue(undefined);

      await service.softDelete(1);

      expect(repository.findById).toHaveBeenCalledWith(1);
      expect(repository.softDelete).toHaveBeenCalledWith(1);
    });

    it('should throw error if server not found', async () => {
      repository.findById.mockResolvedValue(null);

      await expect(service.softDelete(999)).rejects.toThrow(AppException);
    });
  });

  describe('getStatistics', () => {
    it('should return SSH server statistics', async () => {
      const mockStats = {
        total: 10,
        active: 8,
        inactive: 2,
        connected: 6,
        disconnected: 2,
      };
      repository.getStatistics.mockResolvedValue(mockStats);

      const result = await service.getStatistics();

      expect(repository.getStatistics).toHaveBeenCalled();
      expect(result).toEqual(mockStats);
    });
  });

  describe('activate', () => {
    it('should activate SSH server', async () => {
      const activatedServer = { ...mockSshServer, isActive: true };
      repository.findById.mockResolvedValue(mockSshServer);
      repository.update.mockResolvedValue(activatedServer);

      await service.activate(1, 1);

      expect(repository.update).toHaveBeenCalledWith(1, { isActive: true }, 1);
    });
  });

  describe('deactivate', () => {
    it('should deactivate SSH server', async () => {
      const deactivatedServer = { ...mockSshServer, isActive: false };
      repository.findById.mockResolvedValue(mockSshServer);
      repository.update.mockResolvedValue(deactivatedServer);

      await service.deactivate(1, 1);

      expect(repository.update).toHaveBeenCalledWith(1, { isActive: false }, 1);
    });
  });
});
