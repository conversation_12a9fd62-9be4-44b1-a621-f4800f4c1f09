# SSH Server Module Deployment Guide

## Overview

Hướng dẫn deploy SSH Server Management Module vào môi trường production.

## Prerequisites

### System Requirements
- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- Docker (optional)

### Dependencies
- NestJS framework
- TypeORM
- Bull Queue
- SSH2 library
- Encryption service

## Environment Configuration

### Required Environment Variables

```bash
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your_password
DATABASE_NAME=redai_erp

# Redis (for queues)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key
ENCRYPTION_ALGORITHM=aes-256-gcm

# SSH Defaults
SSH_DEFAULT_TIMEOUT=30000
SSH_COMMAND_TIMEOUT=300000
SSH_MAX_RETRIES=3
SSH_RETRY_DELAY=5000

# Queue Settings
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=your_queue_redis_password

# Logging
LOG_LEVEL=info
SSH_LOG_LEVEL=debug

# Security
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h
```

### Optional Environment Variables

```bash
# SSH Connection Pool
SSH_POOL_MAX_SIZE=10
SSH_POOL_MIN_SIZE=2
SSH_POOL_IDLE_TIMEOUT=300000

# Queue Concurrency
SSH_QUEUE_CONCURRENCY=5
DEPLOYMENT_QUEUE_CONCURRENCY=2

# Monitoring
ENABLE_SSH_METRICS=true
METRICS_PORT=9090

# Rate Limiting
SSH_RATE_LIMIT_WINDOW=60000
SSH_RATE_LIMIT_MAX=100
DEPLOYMENT_RATE_LIMIT_MAX=10
```

## Database Setup

### 1. Run Migrations

```bash
# Generate migration (if needed)
npm run migration:generate -- CreateSshServerTables

# Run migrations
npm run migration:run

# Verify migrations
npm run migration:show
```

### 2. Database Schema

The module creates these tables:
- `ssh_servers` - SSH server configurations
- `ssh_execution_logs` - Command execution history
- `deployment_histories` - Deployment tracking

### 3. Indexes

Ensure these indexes exist for performance:

```sql
-- SSH Servers
CREATE INDEX idx_ssh_servers_tenant_id ON ssh_servers(tenant_id);
CREATE INDEX idx_ssh_servers_is_active ON ssh_servers(is_active);
CREATE INDEX idx_ssh_servers_name ON ssh_servers(name);

-- Execution Logs
CREATE INDEX idx_ssh_execution_logs_server_id ON ssh_execution_logs(ssh_server_id);
CREATE INDEX idx_ssh_execution_logs_executed_by ON ssh_execution_logs(executed_by);
CREATE INDEX idx_ssh_execution_logs_status ON ssh_execution_logs(status);
CREATE INDEX idx_ssh_execution_logs_executed_at ON ssh_execution_logs(executed_at);

-- Deployment History
CREATE INDEX idx_deployment_history_project_name ON deployment_histories(project_name);
CREATE INDEX idx_deployment_history_environment ON deployment_histories(environment);
CREATE INDEX idx_deployment_history_status ON deployment_histories(status);
CREATE INDEX idx_deployment_history_started_at ON deployment_histories(started_at);
```

## Application Deployment

### 1. Build Application

```bash
# Install dependencies
npm ci --production

# Build application
npm run build

# Copy necessary files
cp -r dist/ /opt/redai-erp/
cp package.json /opt/redai-erp/
cp .env.production /opt/redai-erp/.env
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --production

# Copy built application
COPY dist/ ./dist/
COPY .env.production ./.env

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001
USER nestjs

EXPOSE 3000

CMD ["node", "dist/main"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: redai_erp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 3. PM2 Deployment

#### ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'redai-erp',
    script: 'dist/main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

```bash
# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
```

## Queue Workers

### 1. Separate Worker Process

```bash
# Start queue workers
npm run start:workers

# Or with PM2
pm2 start ecosystem.workers.config.js
```

#### ecosystem.workers.config.js
```javascript
module.exports = {
  apps: [{
    name: 'ssh-queue-worker',
    script: 'dist/workers/ssh-queue.worker.js',
    instances: 2,
    env: {
      NODE_ENV: 'production',
      WORKER_TYPE: 'ssh-execution'
    }
  }, {
    name: 'deployment-queue-worker',
    script: 'dist/workers/deployment-queue.worker.js',
    instances: 1,
    env: {
      NODE_ENV: 'production',
      WORKER_TYPE: 'deployment'
    }
  }]
};
```

### 2. Queue Monitoring

```bash
# Install Bull Dashboard
npm install -g bull-board

# Start dashboard
bull-board --redis redis://localhost:6379
```

## Security Configuration

### 1. SSH Key Management

```bash
# Generate SSH keys for servers
ssh-keygen -t rsa -b 4096 -f /etc/redai-erp/ssh-keys/server-key

# Set proper permissions
chmod 600 /etc/redai-erp/ssh-keys/server-key
chmod 644 /etc/redai-erp/ssh-keys/server-key.pub
```

### 2. Firewall Rules

```bash
# Allow SSH connections to managed servers
ufw allow out 22/tcp

# Allow application port
ufw allow 3000/tcp

# Allow Redis (if external)
ufw allow 6379/tcp
```

### 3. SSL/TLS Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location /api/ssh-servers {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/ssh-execution {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Increase timeout for long-running deployments
        proxy_read_timeout 1800s;
        proxy_send_timeout 1800s;
    }
}
```

## Monitoring & Logging

### 1. Application Monitoring

```bash
# Install monitoring tools
npm install @nestjs/terminus
npm install prom-client

# Health check endpoint
curl http://localhost:3000/health
```

### 2. Log Configuration

```typescript
// logger.config.ts
export const loggerConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: 'logs/ssh-error.log',
      level: 'error'
    }),
    new winston.transports.File({
      filename: 'logs/ssh-combined.log'
    })
  ]
};
```

### 3. Metrics Collection

```typescript
// metrics.service.ts
@Injectable()
export class MetricsService {
  private sshConnectionsTotal = new Counter({
    name: 'ssh_connections_total',
    help: 'Total SSH connections',
    labelNames: ['server_id', 'status']
  });

  private commandExecutionDuration = new Histogram({
    name: 'ssh_command_execution_duration_seconds',
    help: 'SSH command execution duration',
    labelNames: ['server_id', 'command_type']
  });

  private deploymentDuration = new Histogram({
    name: 'deployment_duration_seconds',
    help: 'Deployment duration',
    labelNames: ['project_name', 'environment', 'status']
  });
}
```

## Backup & Recovery

### 1. Database Backup

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U postgres redai_erp > /backups/redai_erp_$DATE.sql

# Keep only last 30 days
find /backups -name "redai_erp_*.sql" -mtime +30 -delete
```

### 2. SSH Keys Backup

```bash
# Backup SSH keys
tar -czf /backups/ssh-keys_$DATE.tar.gz /etc/redai-erp/ssh-keys/

# Encrypt backup
gpg --cipher-algo AES256 --compress-algo 1 --s2k-cipher-algo AES256 \
    --s2k-digest-algo SHA512 --s2k-mode 3 --s2k-count 65536 \
    --symmetric --output ssh-keys_$DATE.tar.gz.gpg ssh-keys_$DATE.tar.gz
```

### 3. Configuration Backup

```bash
# Backup environment configuration
cp .env.production /backups/env_$DATE.backup
cp ecosystem.config.js /backups/pm2_$DATE.backup
```

## Performance Optimization

### 1. Database Optimization

```sql
-- Optimize queries
ANALYZE ssh_servers;
ANALYZE ssh_execution_logs;
ANALYZE deployment_histories;

-- Vacuum regularly
VACUUM ANALYZE;
```

### 2. Connection Pooling

```typescript
// database.config.ts
export const databaseConfig = {
  type: 'postgres',
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_PORT),
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  extra: {
    max: 20, // Maximum connections
    min: 5,  // Minimum connections
    idle_timeout: 30000,
    connection_timeout: 60000,
  }
};
```

### 3. Redis Optimization

```bash
# Redis configuration
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## Troubleshooting

### 1. Common Issues

#### SSH Connection Failures
```bash
# Check SSH connectivity
ssh -o ConnectTimeout=10 user@server

# Check firewall
telnet server 22

# Check logs
tail -f logs/ssh-error.log
```

#### Queue Processing Issues
```bash
# Check Redis connection
redis-cli ping

# Monitor queue
redis-cli monitor

# Check worker processes
pm2 list
pm2 logs ssh-queue-worker
```

#### Database Performance
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check connections
SELECT * FROM pg_stat_activity;
```

### 2. Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=debug
export SSH_LOG_LEVEL=debug

# Restart application
pm2 restart redai-erp
```

### 3. Health Checks

```bash
# Application health
curl http://localhost:3000/health

# Database health
curl http://localhost:3000/health/database

# Redis health
curl http://localhost:3000/health/redis

# SSH connectivity health
curl http://localhost:3000/health/ssh
```

## Scaling Considerations

### 1. Horizontal Scaling

- Use load balancer for multiple app instances
- Separate queue workers on different servers
- Database read replicas for heavy read workloads

### 2. Vertical Scaling

- Increase memory for SSH connection pools
- More CPU cores for queue processing
- SSD storage for better I/O performance

### 3. Queue Scaling

```typescript
// Scale queue workers based on load
const queueConfig = {
  concurrency: process.env.NODE_ENV === 'production' ? 10 : 2,
  maxStalledCount: 3,
  retryProcessDelay: 5000,
};
```

## Maintenance

### 1. Regular Tasks

```bash
# Weekly log rotation
logrotate /etc/logrotate.d/redai-erp

# Monthly database maintenance
psql -c "VACUUM ANALYZE;"

# Quarterly security updates
npm audit fix
```

### 2. Monitoring Alerts

Set up alerts for:
- High SSH connection failure rate
- Queue processing delays
- Database connection issues
- Disk space usage
- Memory usage

### 3. Update Procedure

```bash
# 1. Backup current version
cp -r /opt/redai-erp /opt/redai-erp.backup

# 2. Stop application
pm2 stop redai-erp

# 3. Deploy new version
npm run build
cp -r dist/* /opt/redai-erp/dist/

# 4. Run migrations
npm run migration:run

# 5. Start application
pm2 start redai-erp

# 6. Verify deployment
curl http://localhost:3000/health
```
