### Test Todo Assignee API
### C<PERSON>n thay đổi {{baseUrl}} và {{authToken}} phù hợp với environment

@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### ===== TODO ASSIGNEE APIs =====

### 1. <PERSON><PERSON><PERSON> nhật người được giao việc (API mới)
PATCH {{baseUrl}}/v1/todos/1/assignee
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "assigneeId": 5
}

### 2. Cập nhật người được giao việc - Bỏ giao việc (set null)
PATCH {{baseUrl}}/v1/todos/1/assignee
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "assigneeId": null
}

### 3. Test với ID không tồn tại (should return 404)
PATCH {{baseUrl}}/v1/todos/999999/assignee
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "assigneeId": 5
}

### 4. Test với assigneeId không hợp lệ (should return validation error)
PATCH {{baseUrl}}/v1/todos/1/assignee
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "assigneeId": "invalid"
}

### 5. Test với assigneeId = 0 (should return validation error)
PATCH {{baseUrl}}/v1/todos/1/assignee
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "assigneeId": 0
}

### ===== SO SÁNH VỚI API CŨ =====

### 6. Cập nhật assignee qua API updateTodo (API cũ - để so sánh)
PATCH {{baseUrl}}/v1/todos/1
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "assigneeId": 5
}

### ===== KIỂM TRA KẾT QUẢ =====

### 7. Lấy chi tiết công việc để kiểm tra assignee đã được cập nhật
GET {{baseUrl}}/v1/todos/1
Authorization: Bearer {{authToken}}
Content-Type: application/json

### ===== TEST PERMISSIONS =====

### 8. Test với user không phải là creator (should return permission error)
### Cần đăng nhập bằng user khác không phải là creator của todo
PATCH {{baseUrl}}/v1/todos/1/assignee
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "assigneeId": 5
}

### ===== NOTES =====
### 
### API mới: PATCH /todos/:id/assignee
### - Chỉ cho phép cập nhật assigneeId
### - Chỉ người tạo công việc mới có quyền thay đổi assignee
### - Hỗ trợ set assigneeId = null để bỏ giao việc
### - Sử dụng service updateTodoAssignee chuyên biệt
### 
### API cũ: PATCH /todos/:id
### - Cho phép cập nhật tất cả fields của todo
### - Bao gồm cả assigneeId nhưng không có logic kiểm tra quyền riêng
### - Sử dụng service updateTodo tổng quát
### 
### Unified ID System:
### - assigneeId có thể là User.id hoặc Employee.id (giống nhau)
### - System sẽ validate assigneeId tồn tại trong hệ thống
### - Hỗ trợ tenant isolation
