import { Injectable, Logger } from '@nestjs/common';
import { SshCommandService } from './ssh-command.service';
import { DeploymentHistoryRepository } from '../repositories/deployment-history.repository';
import { SshServerRepository } from '../repositories/ssh-server.repository';
import { 
  DeploymentConfig,
  DeploymentResult,
  GitDeploymentConfig,
  DockerDeploymentConfig,
  NpmDeploymentConfig,
  CustomDeploymentConfig,
  RollbackDeploymentConfig,
  AnyDeploymentConfig,
  DeploymentValidation,
  DeploymentStatistics,
} from '../interfaces/deployment-config.interface';
import { SshBatchCommand } from '../interfaces/ssh-connection.interface';
import { SSH_COMMANDS } from '../constants/ssh-commands.constants';

/**
 * Service quản lý deployment automation
 */
@Injectable()
export class DeploymentService {
  private readonly logger = new Logger(DeploymentService.name);

  constructor(
    private readonly sshCommandService: SshCommandService,
    private readonly deploymentHistoryRepository: DeploymentHistoryRepository,
    private readonly sshServerRepository: SshServerRepository,
  ) {}

  /**
   * Thực hiện deployment
   * @param sshServerId ID server SSH
   * @param config Cấu hình deployment
   * @param deployedBy ID người deploy
   * @param jobId ID job queue (optional)
   * @returns Kết quả deployment
   */
  async deploy(
    sshServerId: number,
    config: AnyDeploymentConfig,
    deployedBy: number,
    jobId?: string,
  ): Promise<DeploymentResult> {
    // Validate cấu hình deployment
    const validation = await this.validateDeploymentConfig(config);
    if (!validation.isValid) {
      throw new Error(`Cấu hình deployment không hợp lệ: ${validation.errors.join(', ')}`);
    }

    // Tạo deployment history
    const deployment = await this.deploymentHistoryRepository.create({
      sshServerId,
      projectName: config.projectName,
      repositoryUrl: config.repositoryUrl,
      branch: config.branch,
      commitHash: config.commitHash,
      tag: config.tag,
      deploymentType: config.deploymentType,
      environment: config.environment,
      projectPath: config.projectPath,
      buildScript: config.buildScript,
      postDeployScript: config.postDeployScript,
      deploymentConfig: JSON.stringify(config),
      status: 'pending',
      jobId,
      deployedBy,
    });

    this.logger.log(`Bắt đầu deployment ${config.projectName} (ID: ${deployment.id}) trên server ${sshServerId}`);

    try {
      // Cập nhật trạng thái đang chạy
      await this.deploymentHistoryRepository.updateStatus(deployment.id, 'running');

      // Tạo danh sách lệnh deployment
      const commands = await this.generateDeploymentCommands(config);

      // Thực thi deployment commands
      const { result, logIds } = await this.sshCommandService.executeBatchCommands(
        sshServerId,
        commands,
        deployedBy,
        {
          executionType: 'deployment',
          jobId,
          metadata: {
            deploymentId: deployment.id,
            projectName: config.projectName,
            deploymentType: config.deploymentType,
          },
        },
      );

      // Cập nhật kết quả deployment
      const status = result.overallSuccess ? 'completed' : 'failed';
      const logs = this.formatDeploymentLogs(result);
      const errorMessage = result.overallSuccess ? undefined : this.extractErrorMessage(result);

      await this.deploymentHistoryRepository.updateStatus(
        deployment.id,
        status,
        logs,
        errorMessage,
        JSON.stringify({
          executionLogIds: logIds,
          totalCommands: result.totalCommands,
          successCount: result.successCount,
          failureCount: result.failureCount,
          executionTime: result.totalExecutionTime,
        }),
      );

      this.logger.log(`Hoàn thành deployment ${config.projectName} (ID: ${deployment.id}): ${status}`);

      return {
        deploymentId: deployment.id,
        status,
        startedAt: deployment.startedAt,
        completedAt: Date.now(),
        logs,
        errorMessage,
        metadata: {
          executionLogIds: logIds,
          totalCommands: result.totalCommands,
          successCount: result.successCount,
          failureCount: result.failureCount,
          executionTime: result.totalExecutionTime,
        },
        executionLogIds: logIds,
      };

    } catch (error) {
      // Cập nhật lỗi deployment
      await this.deploymentHistoryRepository.updateStatus(
        deployment.id,
        'failed',
        '',
        error.message,
      );

      this.logger.error(`Lỗi deployment ${config.projectName} (ID: ${deployment.id}): ${error.message}`);

      return {
        deploymentId: deployment.id,
        status: 'failed',
        startedAt: deployment.startedAt,
        completedAt: Date.now(),
        logs: '',
        errorMessage: error.message,
        executionLogIds: [],
      };
    }
  }

  /**
   * Rollback deployment
   * @param sshServerId ID server SSH
   * @param deploymentId ID deployment cần rollback
   * @param deployedBy ID người thực hiện rollback
   * @param jobId ID job queue (optional)
   * @returns Kết quả rollback
   */
  async rollback(
    sshServerId: number,
    deploymentId: number,
    deployedBy: number,
    jobId?: string,
  ): Promise<DeploymentResult> {
    // Lấy thông tin deployment cần rollback
    const targetDeployment = await this.deploymentHistoryRepository.findById(deploymentId);
    if (!targetDeployment) {
      throw new Error(`Deployment với ID ${deploymentId} không tồn tại`);
    }

    if (targetDeployment.status !== 'completed') {
      throw new Error(`Chỉ có thể rollback deployment đã hoàn thành thành công`);
    }

    // Tìm deployment thành công trước đó
    const previousDeployment = await this.deploymentHistoryRepository.findLastSuccessfulDeployment(
      targetDeployment.projectName,
      targetDeployment.environment,
    );

    if (!previousDeployment || previousDeployment.id === deploymentId) {
      throw new Error(`Không tìm thấy deployment trước đó để rollback`);
    }

    // Tạo rollback config
    const rollbackConfig: RollbackDeploymentConfig = {
      projectName: targetDeployment.projectName,
      deploymentType: 'rollback',
      environment: targetDeployment.environment,
      projectPath: targetDeployment.projectPath || '',
      previousDeploymentId: previousDeployment.id,
      createBackupBeforeRollback: true,
    };

    this.logger.log(`Bắt đầu rollback deployment ${deploymentId} về deployment ${previousDeployment.id}`);

    return this.deploy(sshServerId, rollbackConfig, deployedBy, jobId);
  }

  /**
   * Lấy thống kê deployment
   * @param sshServerId ID server SSH (optional)
   * @param projectName Tên dự án (optional)
   * @param fromDate Từ ngày (optional)
   * @param toDate Đến ngày (optional)
   * @returns Thống kê deployment
   */
  async getDeploymentStatistics(
    sshServerId?: number,
    projectName?: string,
    fromDate?: number,
    toDate?: number,
  ): Promise<DeploymentStatistics> {
    if (projectName) {
      return this.deploymentHistoryRepository.getStatisticsByProject(projectName, fromDate, toDate);
    }

    // TODO: Implement server-wide statistics
    // Tạm thời return default statistics
    return {
      totalDeployments: 0,
      successfulDeployments: 0,
      failedDeployments: 0,
      successRate: 0,
      averageDeploymentTime: 0,
    };
  }

  /**
   * Validate cấu hình deployment
   * @param config Cấu hình deployment
   * @returns Kết quả validation
   */
  async validateDeploymentConfig(config: AnyDeploymentConfig): Promise<DeploymentValidation> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate chung
    if (!config.projectName?.trim()) {
      errors.push('Tên dự án không được để trống');
    }

    if (!config.projectPath?.trim()) {
      errors.push('Đường dẫn dự án không được để trống');
    }

    // Validate theo loại deployment
    switch (config.deploymentType) {
      case 'git_pull':
      case 'git_clone':
        this.validateGitDeployment(config as GitDeploymentConfig, errors, warnings);
        break;
      case 'docker_build':
        this.validateDockerDeployment(config as DockerDeploymentConfig, errors, warnings);
        break;
      case 'npm_build':
        this.validateNpmDeployment(config as NpmDeploymentConfig, errors, warnings);
        break;
      case 'custom':
        this.validateCustomDeployment(config as CustomDeploymentConfig, errors, warnings);
        break;
      case 'rollback':
        this.validateRollbackDeployment(config as RollbackDeploymentConfig, errors, warnings);
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Tạo danh sách lệnh deployment
   * @param config Cấu hình deployment
   * @returns Danh sách lệnh
   */
  private async generateDeploymentCommands(config: AnyDeploymentConfig): Promise<SshBatchCommand[]> {
    const commands: SshBatchCommand[] = [];

    // Chuyển đến thư mục dự án
    commands.push({
      command: `cd ${config.projectPath}`,
      description: 'Chuyển đến thư mục dự án',
      workingDirectory: config.projectPath,
    });

    switch (config.deploymentType) {
      case 'git_pull':
        commands.push(...this.generateGitPullCommands(config as GitDeploymentConfig));
        break;
      case 'git_clone':
        commands.push(...this.generateGitCloneCommands(config as GitDeploymentConfig));
        break;
      case 'docker_build':
        commands.push(...this.generateDockerCommands(config as DockerDeploymentConfig));
        break;
      case 'npm_build':
        commands.push(...this.generateNpmCommands(config as NpmDeploymentConfig));
        break;
      case 'custom':
        commands.push(...this.generateCustomCommands(config as CustomDeploymentConfig));
        break;
      case 'rollback':
        commands.push(...this.generateRollbackCommands(config as RollbackDeploymentConfig));
        break;
    }

    // Thêm post-deployment script nếu có
    if (config.postDeployScript) {
      commands.push({
        command: config.postDeployScript,
        description: 'Thực thi post-deployment script',
        workingDirectory: config.projectPath,
        environment: config.environmentVariables,
      });
    }

    return commands;
  }

  /**
   * Tạo lệnh Git pull
   */
  private generateGitPullCommands(config: GitDeploymentConfig): SshBatchCommand[] {
    const commands: SshBatchCommand[] = [];

    if (config.stashChanges) {
      commands.push({
        command: SSH_COMMANDS.GIT.STASH,
        description: 'Stash local changes',
        workingDirectory: config.projectPath,
      });
    }

    if (config.branch) {
      commands.push({
        command: SSH_COMMANDS.GIT.CHECKOUT(config.branch),
        description: `Checkout branch ${config.branch}`,
        workingDirectory: config.projectPath,
      });
    }

    commands.push({
      command: config.forcePull ? 'git pull --force' : SSH_COMMANDS.GIT.PULL,
      description: 'Pull latest changes',
      workingDirectory: config.projectPath,
    });

    if (config.commitHash) {
      commands.push({
        command: SSH_COMMANDS.GIT.RESET_HARD(config.commitHash),
        description: `Reset to commit ${config.commitHash}`,
        workingDirectory: config.projectPath,
      });
    }

    return commands;
  }

  /**
   * Tạo lệnh Git clone
   */
  private generateGitCloneCommands(config: GitDeploymentConfig): SshBatchCommand[] {
    const commands: SshBatchCommand[] = [];

    if (config.repositoryUrl) {
      const cloneCommand = config.branch 
        ? `git clone -b ${config.branch} ${config.repositoryUrl} ${config.projectPath}`
        : SSH_COMMANDS.GIT.CLONE(config.repositoryUrl, config.projectPath);

      commands.push({
        command: cloneCommand,
        description: `Clone repository ${config.repositoryUrl}`,
      });
    }

    return commands;
  }

  /**
   * Tạo lệnh Docker
   */
  private generateDockerCommands(config: DockerDeploymentConfig): SshBatchCommand[] {
    const commands: SshBatchCommand[] = [];

    if (config.composeFile) {
      commands.push({
        command: 'docker-compose down',
        description: 'Stop existing containers',
        workingDirectory: config.projectPath,
      });

      commands.push({
        command: 'docker-compose build',
        description: 'Build Docker images',
        workingDirectory: config.projectPath,
      });

      commands.push({
        command: 'docker-compose up -d',
        description: 'Start containers',
        workingDirectory: config.projectPath,
      });
    } else {
      const imageTag = config.imageTag || config.projectName;
      
      commands.push({
        command: SSH_COMMANDS.DOCKER.BUILD(imageTag, config.dockerfilePath || '.'),
        description: `Build Docker image ${imageTag}`,
        workingDirectory: config.projectPath,
      });

      if (config.containerName) {
        commands.push({
          command: SSH_COMMANDS.DOCKER.STOP(config.containerName),
          description: `Stop container ${config.containerName}`,
          continueOnError: true,
        });

        commands.push({
          command: SSH_COMMANDS.DOCKER.REMOVE(config.containerName),
          description: `Remove container ${config.containerName}`,
          continueOnError: true,
        });

        const runOptions = this.buildDockerRunOptions(config);
        commands.push({
          command: SSH_COMMANDS.DOCKER.RUN(imageTag, runOptions),
          description: `Run container ${config.containerName}`,
          workingDirectory: config.projectPath,
        });
      }
    }

    return commands;
  }

  /**
   * Tạo lệnh NPM
   */
  private generateNpmCommands(config: NpmDeploymentConfig): SshBatchCommand[] {
    const commands: SshBatchCommand[] = [];
    const packageManager = config.packageManager || 'npm';

    // Install dependencies
    const installCommand = packageManager === 'npm' ? 'npm ci' : 
                          packageManager === 'yarn' ? 'yarn install --frozen-lockfile' :
                          'pnpm install --frozen-lockfile';

    commands.push({
      command: installCommand,
      description: 'Install dependencies',
      workingDirectory: config.projectPath,
      environment: config.environmentVariables,
    });

    // Build
    const buildCommand = config.buildCommand || 
                        (packageManager === 'npm' ? 'npm run build' :
                         packageManager === 'yarn' ? 'yarn build' : 'pnpm build');

    commands.push({
      command: buildCommand,
      description: 'Build application',
      workingDirectory: config.projectPath,
      environment: config.environmentVariables,
    });

    // PM2 restart
    if (config.pm2AppName) {
      commands.push({
        command: SSH_COMMANDS.PM2.RESTART(config.pm2AppName),
        description: `Restart PM2 app ${config.pm2AppName}`,
        workingDirectory: config.projectPath,
      });
    } else if (config.startCommand) {
      commands.push({
        command: config.startCommand,
        description: 'Start application',
        workingDirectory: config.projectPath,
        environment: config.environmentVariables,
      });
    }

    return commands;
  }

  /**
   * Tạo lệnh custom
   */
  private generateCustomCommands(config: CustomDeploymentConfig): SshBatchCommand[] {
    return config.customCommands.map(cmd => ({
      command: cmd.command,
      description: cmd.description || cmd.command,
      workingDirectory: cmd.workingDirectory || config.projectPath,
      environment: { ...config.environmentVariables, ...cmd.environment },
      continueOnError: cmd.continueOnError,
    }));
  }

  /**
   * Tạo lệnh rollback
   */
  private generateRollbackCommands(config: RollbackDeploymentConfig): SshBatchCommand[] {
    const commands: SshBatchCommand[] = [];

    if (config.createBackupBeforeRollback) {
      const backupPath = `${config.projectPath}_backup_${Date.now()}`;
      commands.push({
        command: `cp -r ${config.projectPath} ${backupPath}`,
        description: 'Create backup before rollback',
      });
    }

    // TODO: Implement actual rollback logic based on previous deployment
    commands.push({
      command: 'echo "Rollback implementation needed"',
      description: 'Rollback to previous deployment',
      workingDirectory: config.projectPath,
    });

    return commands;
  }

  /**
   * Validate Git deployment
   */
  private validateGitDeployment(config: GitDeploymentConfig, errors: string[], warnings: string[]): void {
    if (config.deploymentType === 'git_clone' && !config.repositoryUrl) {
      errors.push('Repository URL là bắt buộc cho git clone');
    }
  }

  /**
   * Validate Docker deployment
   */
  private validateDockerDeployment(config: DockerDeploymentConfig, errors: string[], warnings: string[]): void {
    if (!config.composeFile && !config.imageTag) {
      warnings.push('Nên cung cấp compose file hoặc image tag');
    }
  }

  /**
   * Validate NPM deployment
   */
  private validateNpmDeployment(config: NpmDeploymentConfig, errors: string[], warnings: string[]): void {
    if (!config.packageManager) {
      warnings.push('Package manager không được chỉ định, sẽ sử dụng npm mặc định');
    }
  }

  /**
   * Validate Custom deployment
   */
  private validateCustomDeployment(config: CustomDeploymentConfig, errors: string[], warnings: string[]): void {
    if (!config.customCommands || config.customCommands.length === 0) {
      errors.push('Custom deployment cần ít nhất một lệnh');
    }
  }

  /**
   * Validate Rollback deployment
   */
  private validateRollbackDeployment(config: RollbackDeploymentConfig, errors: string[], warnings: string[]): void {
    if (!config.previousDeploymentId) {
      errors.push('Previous deployment ID là bắt buộc cho rollback');
    }
  }

  /**
   * Build Docker run options
   */
  private buildDockerRunOptions(config: DockerDeploymentConfig): string {
    const options: string[] = [];

    if (config.containerName) {
      options.push(`--name ${config.containerName}`);
    }

    options.push('-d'); // Detached mode

    if (config.portMapping) {
      Object.entries(config.portMapping).forEach(([host, container]) => {
        options.push(`-p ${host}:${container}`);
      });
    }

    if (config.volumeMapping) {
      Object.entries(config.volumeMapping).forEach(([host, container]) => {
        options.push(`-v ${host}:${container}`);
      });
    }

    return options.join(' ');
  }

  /**
   * Format deployment logs
   */
  private formatDeploymentLogs(result: any): string {
    const logs: string[] = [];
    
    result.results.forEach((cmdResult: any, index: number) => {
      logs.push(`=== Command ${index + 1}: ${cmdResult.description || cmdResult.command} ===`);
      logs.push(`Exit Code: ${cmdResult.exitCode}`);
      logs.push(`Execution Time: ${cmdResult.executionTime}ms`);
      
      if (cmdResult.stdout) {
        logs.push('STDOUT:');
        logs.push(cmdResult.stdout);
      }
      
      if (cmdResult.stderr) {
        logs.push('STDERR:');
        logs.push(cmdResult.stderr);
      }
      
      logs.push('');
    });

    return logs.join('\n');
  }

  /**
   * Extract error message from batch result
   */
  private extractErrorMessage(result: any): string {
    const failedCommands = result.results.filter((r: any) => !r.success);
    if (failedCommands.length === 0) return '';

    const errors = failedCommands.map((cmd: any) => 
      `${cmd.description || cmd.command}: ${cmd.stderr || cmd.error || 'Unknown error'}`
    );

    return errors.join('; ');
  }
}
