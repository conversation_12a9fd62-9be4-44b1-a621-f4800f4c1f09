# SSH Server Management Module

Module quản lý SSH servers và deployment automation cho hệ thống RedAI ERP.

## Tổng quan

SSH Server Module cung cấp các tính năng:

- **SSH Server Management**: Qu<PERSON>n lý thông tin kết nối SSH servers
- **Command Execution**: Thực thi lệnh SSH từ xa với logging
- **Deployment Automation**: Tự động hóa deployment với nhiều loại khác nhau
- **Queue Support**: Xử lý bất đồng bộ với Bull Queue
- **Chat Integration**: Tích hợp với multi-agent chat system

## Cấu trúc Module

```
src/modules/ssh-server/
├── entities/                    # Database entities
│   ├── ssh-server.entity.ts
│   ├── ssh-execution-log.entity.ts
│   └── deployment-history.entity.ts
├── repositories/               # Data access layer
│   ├── ssh-server.repository.ts
│   ├── ssh-execution-log.repository.ts
│   └── deployment-history.repository.ts
├── services/                   # Business logic
│   ├── ssh-connection.service.ts
│   ├── ssh-command.service.ts
│   ├── deployment.service.ts
│   ├── ssh-server.service.ts
│   └── ssh-queue.service.ts
├── controllers/                # API endpoints
│   ├── ssh-server.controller.ts
│   └── ssh-execution.controller.ts
├── processors/                 # Queue processors
│   ├── ssh-execution.processor.ts
│   └── deployment.processor.ts
├── tools/                      # Chat system tools
│   ├── ssh-server-management.tool.ts
│   ├── ssh-command-execution.tool.ts
│   ├── deployment-automation.tool.ts
│   └── ssh-tools.provider.ts
├── dtos/                       # Data transfer objects
│   ├── requests/
│   └── responses/
├── interfaces/                 # TypeScript interfaces
├── constants/                  # Constants và templates
└── README.md
```

## Tính năng chính

### 1. SSH Server Management

#### Tạo SSH Server
```typescript
const createDto: CreateSshServerDto = {
  name: 'Production Server 1',
  host: '*************',
  port: 22,
  username: 'ubuntu',
  password: 'secure-password', // Sẽ được mã hóa
  description: 'Production server for web application'
};

const server = await sshServerService.create(createDto, userId);
```

#### Test Kết nối
```typescript
const result = await sshServerService.testConnection(serverId);
console.log(result.success); // true/false
console.log(result.systemInfo); // Thông tin hệ thống
```

### 2. Command Execution

#### Thực thi lệnh đơn lẻ
```typescript
const { result, logId } = await sshCommandService.executeCommand(
  serverId,
  'ls -la',
  userId,
  {
    workingDirectory: '/home/<USER>',
    timeout: 30000
  }
);
```

#### Thực thi batch commands
```typescript
const commands = [
  { command: 'git pull', description: 'Pull latest code' },
  { command: 'npm install', description: 'Install dependencies' },
  { command: 'npm run build', description: 'Build application' }
];

const { result, logIds } = await sshCommandService.executeBatchCommands(
  serverId,
  commands,
  userId
);
```

### 3. Deployment Automation

#### Git Pull Deployment
```typescript
const deploymentConfig: GitDeploymentConfig = {
  projectName: 'my-web-app',
  deploymentType: 'git_pull',
  environment: 'production',
  projectPath: '/home/<USER>/my-web-app',
  branch: 'main',
  buildScript: 'npm run build:prod',
  postDeployScript: 'pm2 restart app'
};

const result = await deploymentService.deploy(
  serverId,
  deploymentConfig,
  userId
);
```

#### Docker Deployment
```typescript
const deploymentConfig: DockerDeploymentConfig = {
  projectName: 'my-docker-app',
  deploymentType: 'docker_build',
  environment: 'production',
  projectPath: '/home/<USER>/my-docker-app',
  composeFile: 'docker-compose.prod.yml'
};
```

#### Rollback Deployment
```typescript
const result = await deploymentService.rollback(
  serverId,
  deploymentId,
  userId
);
```

### 4. Queue Operations

#### Thêm job vào queue
```typescript
// SSH Command job
const jobId = await sshQueueService.addExecuteCommandJob(
  serverId,
  'systemctl status nginx',
  userId,
  { priority: 5 }
);

// Deployment job
const jobId = await sshQueueService.addDeployProjectJob(
  serverId,
  deploymentConfig,
  userId,
  { priority: 10 }
);
```

### 5. Chat Integration

SSH Module tích hợp với multi-agent chat system thông qua SSH Agent:

```typescript
// Các lệnh chat có thể sử dụng:
"Tạo SSH server mới với tên Production-1"
"Test kết nối SSH server ID 1"
"Thực thi lệnh 'df -h' trên server 1"
"Deploy dự án my-app lên production"
"Rollback deployment 123"
"Lấy thống kê SSH servers"
```

## API Endpoints

### SSH Server Management
- `GET /ssh-servers` - Lấy danh sách SSH servers
- `POST /ssh-servers` - Tạo SSH server mới
- `GET /ssh-servers/:id` - Lấy thông tin SSH server
- `PUT /ssh-servers/:id` - Cập nhật SSH server
- `DELETE /ssh-servers/:id` - Xóa SSH server
- `POST /ssh-servers/:id/test-connection` - Test kết nối
- `GET /ssh-servers/statistics/overview` - Thống kê tổng quan

### SSH Execution
- `POST /ssh-execution/command` - Thực thi lệnh đơn lẻ
- `POST /ssh-execution/batch` - Thực thi batch commands
- `POST /ssh-execution/deploy` - Deploy dự án
- `POST /ssh-execution/rollback` - Rollback deployment
- `POST /ssh-execution/deploy-template` - Deploy với template
- `GET /ssh-execution/logs` - Lấy execution logs
- `GET /ssh-execution/deployment-templates` - Lấy deployment templates

## Security

### Mã hóa Credentials
Tất cả thông tin nhạy cảm (password, private key, passphrase) được mã hóa trước khi lưu database:

```typescript
// Tự động mã hóa khi tạo/cập nhật
const server = await sshServerService.create({
  password: 'plain-password' // Sẽ được mã hóa
}, userId);
```

### Tenant Isolation
Tất cả operations được phân tách theo tenant:

```typescript
// Tự động inject tenantId từ user context
const servers = await sshServerService.findWithPagination({
  // Chỉ lấy servers của tenant hiện tại
});
```

### Validation & Authorization
- Validate input với class-validator
- Check quyền truy cập SSH server
- Audit logging cho tất cả operations

## Queue System

### SSH Execution Queue
Xử lý các tác vụ SSH bất đồng bộ:

```typescript
// Job types
- EXECUTE_COMMAND: Thực thi lệnh đơn lẻ
- EXECUTE_BATCH_COMMANDS: Thực thi batch commands  
- HEALTH_CHECK: Kiểm tra sức khỏe server
```

### Deployment Queue
Xử lý deployment operations:

```typescript
// Job types
- DEPLOY_PROJECT: Deploy dự án
- ROLLBACK_DEPLOYMENT: Rollback deployment
- DEPLOY_WITH_TEMPLATE: Deploy với template
```

### Monitoring
- Job progress tracking
- Error handling và retry logic
- Queue statistics và monitoring

## Deployment Templates

### Built-in Templates
- **NODEJS**: Standard Node.js deployment
- **REACT**: React application deployment
- **DOCKER**: Docker container deployment
- **NESTJS**: NestJS application deployment
- **CUSTOM**: Custom command deployment

### Template Usage
```typescript
const { result } = await sshCommandService.executeDeploymentTemplate(
  serverId,
  'NODEJS',
  userId,
  undefined, // custom commands
  { metadata: { projectName: 'my-app' } }
);
```

## Error Handling

### SSH Connection Errors
```typescript
try {
  const result = await sshConnectionService.createConnection(config);
} catch (error) {
  if (error.message.includes('SSH_CONNECTION_FAILED')) {
    // Handle connection failure
  }
}
```

### Command Execution Errors
```typescript
const result = await sshCommandService.executeCommand(/*...*/);
if (!result.success) {
  console.log('Command failed:', result.error);
  console.log('Exit code:', result.exitCode);
  console.log('Stderr:', result.stderr);
}
```

## Best Practices

### 1. SSH Security
- Sử dụng SSH keys thay vì passwords khi có thể
- Cấu hình proper SSH timeouts
- Regular rotation của credentials
- Monitor failed connection attempts

### 2. Deployment Safety
- Luôn test trên staging trước production
- Backup trước khi deploy
- Sử dụng rollback khi cần thiết
- Monitor deployment logs

### 3. Performance
- Sử dụng queue cho long-running operations
- Batch multiple commands khi có thể
- Set appropriate timeouts
- Clean up old logs định kỳ

### 4. Monitoring
- Track SSH connection health
- Monitor deployment success rates
- Set up alerts cho failed operations
- Regular review của execution logs

## Configuration

### Environment Variables
```bash
# SSH defaults
SSH_DEFAULT_TIMEOUT=30000
SSH_COMMAND_TIMEOUT=300000
SSH_MAX_RETRIES=3

# Queue settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Encryption
ENCRYPTION_KEY=your-encryption-key
```

### Database Migration
```bash
# Chạy migrations
npm run migration:run

# Tạo migration mới
npm run migration:create -- CreateSshServerTable
```

## Testing

### Unit Tests
```bash
# Chạy tests
npm run test src/modules/ssh-server

# Test coverage
npm run test:cov src/modules/ssh-server
```

### Integration Tests
```bash
# Test với database
npm run test:e2e ssh-server
```

### Manual Testing
```bash
# Test SSH connection
curl -X POST /api/ssh-servers/1/test-connection

# Test command execution
curl -X POST /api/ssh-execution/command \
  -H "Content-Type: application/json" \
  -d '{"sshServerId": 1, "command": "uptime"}'
```

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Check network connectivity
   - Verify credentials
   - Check SSH server status
   - Review firewall settings

2. **Command Timeout**
   - Increase command timeout
   - Check server performance
   - Review command complexity

3. **Deployment Failed**
   - Check deployment logs
   - Verify project path
   - Check permissions
   - Review build scripts

4. **Queue Issues**
   - Check Redis connection
   - Review queue statistics
   - Check worker processes
   - Monitor memory usage

### Debug Mode
```typescript
// Enable debug logging
process.env.LOG_LEVEL = 'debug';

// Check SSH connection details
const connectionInfo = sshConnectionService.getConnectionInfo(connectionId);
```

## Contributing

1. Follow TypeScript best practices
2. Add proper error handling
3. Include unit tests
4. Update documentation
5. Follow security guidelines
6. Test với multiple SSH servers
7. Validate input thoroughly
8. Handle edge cases properly
