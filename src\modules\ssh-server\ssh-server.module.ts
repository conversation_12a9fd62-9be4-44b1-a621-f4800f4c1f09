import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '@/modules/auth/auth.module';
import { ServicesModule } from '@/shared/services/services.module';

// Entities
import { SshServer } from './entities/ssh-server.entity';
import { SshExecutionLog } from './entities/ssh-execution-log.entity';
import { DeploymentHistory } from './entities/deployment-history.entity';

// Repositories
import { SshServerRepository } from './repositories/ssh-server.repository';
import { SshExecutionLogRepository } from './repositories/ssh-execution-log.repository';
import { DeploymentHistoryRepository } from './repositories/deployment-history.repository';

// Services
import { SshConnectionService } from './services/ssh-connection.service';
import { SshCommandService } from './services/ssh-command.service';
import { DeploymentService } from './services/deployment.service';
import { SshServerService } from './services/ssh-server.service';

// Controllers
import { SshServerController } from './controllers/ssh-server.controller';
import { SshExecutionController } from './controllers/ssh-execution.controller';

// Tools
import { SshServerManagementTool } from './tools/ssh-server-management.tool';
import { SshCommandExecutionTool } from './tools/ssh-command-execution.tool';
import { DeploymentAutomationTool } from './tools/deployment-automation.tool';
import { SshToolsProvider } from './tools/ssh-tools.provider';

/**
 * Module quản lý SSH servers và deployment automation
 */
@Global()
@Module({
  imports: [
    // Import TypeORM entities
    TypeOrmModule.forFeature([
      SshServer,
      SshExecutionLog,
      DeploymentHistory,
    ]),
    // Import other modules for dependencies
    AuthModule,
    ServicesModule,
  ],
  controllers: [
    SshServerController,
    SshExecutionController,
  ],
  providers: [
    // Repositories
    SshServerRepository,
    SshExecutionLogRepository,
    DeploymentHistoryRepository,

    // Services
    SshConnectionService,
    SshCommandService,
    DeploymentService,
    SshServerService,

    // Tools
    SshServerManagementTool,
    SshCommandExecutionTool,
    DeploymentAutomationTool,
    SshToolsProvider,
  ],
  exports: [
    // Export repositories for other modules
    SshServerRepository,
    SshExecutionLogRepository,
    DeploymentHistoryRepository,

    // Export services for other modules
    SshConnectionService,
    SshCommandService,
    DeploymentService,
    SshServerService,

    // Export tools for chat system
    SshServerManagementTool,
    SshCommandExecutionTool,
    DeploymentAutomationTool,
    SshToolsProvider,

    // Export TypeORM module
    TypeOrmModule,
  ],
})
export class SshServerModule {}
