import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO response cho kết quả thực thi lệnh SSH
 */
export class CommandResultResponseDto {
  /**
   * ID log thực thi
   */
  @ApiProperty({
    description: 'ID log thực thi',
    example: 123,
  })
  logId: number;

  /**
   * Lệnh đã thực thi
   */
  @ApiProperty({
    description: 'Lệnh đã thực thi',
    example: 'ls -la',
  })
  command: string;

  /**
   * Output của lệnh
   */
  @ApiProperty({
    description: 'Output của lệnh',
    example: 'total 24\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan 1 12:00 .\ndrwxr-xr-x 5 <USER> <GROUP> 4096 Jan 1 11:00 ..',
  })
  stdout: string;

  /**
   * Error output của lệnh
   */
  @ApiProperty({
    description: 'Error output của lệnh',
    example: '',
  })
  stderr: string;

  /**
   * Exit code của lệnh
   */
  @ApiProperty({
    description: 'Exit code của lệnh',
    example: 0,
  })
  exitCode: number;

  /**
   * Thời gian thực thi (ms)
   */
  @ApiProperty({
    description: 'Thời gian thực thi (ms)',
    example: 1500,
  })
  executionTime: number;

  /**
   * Trạng thái thành công
   */
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  success: boolean;

  /**
   * Thông báo lỗi (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Thông báo lỗi (nếu có)',
    example: null,
  })
  error?: string;

  /**
   * Thời gian thực thi
   */
  @ApiProperty({
    description: 'Thời gian thực thi',
    example: 1640995200000,
  })
  executedAt: number;
}

/**
 * DTO response cho kết quả batch commands
 */
export class BatchCommandResultResponseDto {
  /**
   * Danh sách ID logs thực thi
   */
  @ApiProperty({
    description: 'Danh sách ID logs thực thi',
    example: [123, 124, 125],
  })
  logIds: number[];

  /**
   * Tổng số lệnh
   */
  @ApiProperty({
    description: 'Tổng số lệnh',
    example: 3,
  })
  totalCommands: number;

  /**
   * Số lệnh thành công
   */
  @ApiProperty({
    description: 'Số lệnh thành công',
    example: 2,
  })
  successCount: number;

  /**
   * Số lệnh thất bại
   */
  @ApiProperty({
    description: 'Số lệnh thất bại',
    example: 1,
  })
  failureCount: number;

  /**
   * Kết quả từng lệnh
   */
  @ApiProperty({
    description: 'Kết quả từng lệnh',
    type: [CommandResultResponseDto],
  })
  results: Array<CommandResultResponseDto & { description?: string }>;

  /**
   * Tổng thời gian thực thi (ms)
   */
  @ApiProperty({
    description: 'Tổng thời gian thực thi (ms)',
    example: 5000,
  })
  totalExecutionTime: number;

  /**
   * Trạng thái tổng thể
   */
  @ApiProperty({
    description: 'Trạng thái tổng thể',
    example: false,
  })
  overallSuccess: boolean;

  /**
   * Thời gian bắt đầu
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu',
    example: 1640995200000,
  })
  startedAt: number;

  /**
   * Thời gian hoàn thành
   */
  @ApiProperty({
    description: 'Thời gian hoàn thành',
    example: 1640995205000,
  })
  completedAt: number;
}

/**
 * DTO response cho execution log
 */
export class ExecutionLogResponseDto {
  /**
   * ID log
   */
  @ApiProperty({
    description: 'ID log',
    example: 123,
  })
  id: number;

  /**
   * ID server SSH
   */
  @ApiProperty({
    description: 'ID server SSH',
    example: 1,
  })
  sshServerId: number;

  /**
   * Lệnh đã thực thi
   */
  @ApiProperty({
    description: 'Lệnh đã thực thi',
    example: 'git pull',
  })
  command: string;

  /**
   * Thư mục làm việc
   */
  @ApiPropertyOptional({
    description: 'Thư mục làm việc',
    example: '/home/<USER>/project',
  })
  workingDirectory?: string;

  /**
   * Output của lệnh
   */
  @ApiPropertyOptional({
    description: 'Output của lệnh',
    example: 'Already up to date.',
  })
  output?: string;

  /**
   * Error output của lệnh
   */
  @ApiPropertyOptional({
    description: 'Error output của lệnh',
    example: null,
  })
  errorOutput?: string;

  /**
   * Exit code
   */
  @ApiPropertyOptional({
    description: 'Exit code',
    example: 0,
  })
  exitCode?: number;

  /**
   * Thời gian thực thi (ms)
   */
  @ApiPropertyOptional({
    description: 'Thời gian thực thi (ms)',
    example: 2000,
  })
  executionTimeMs?: number;

  /**
   * Trạng thái thực thi
   */
  @ApiProperty({
    description: 'Trạng thái thực thi',
    enum: ['pending', 'running', 'completed', 'failed', 'timeout', 'cancelled'],
    example: 'completed',
  })
  status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout' | 'cancelled';

  /**
   * ID job queue
   */
  @ApiPropertyOptional({
    description: 'ID job queue',
    example: 'job_123',
  })
  jobId?: string;

  /**
   * Loại thực thi
   */
  @ApiProperty({
    description: 'Loại thực thi',
    enum: ['manual', 'scheduled', 'deployment', 'health_check', 'batch'],
    example: 'manual',
  })
  executionType: 'manual' | 'scheduled' | 'deployment' | 'health_check' | 'batch';

  /**
   * Metadata
   */
  @ApiPropertyOptional({
    description: 'Metadata',
    example: { description: 'Pull latest code' },
  })
  metadata?: any;

  /**
   * ID người thực thi
   */
  @ApiProperty({
    description: 'ID người thực thi',
    example: 1,
  })
  executedBy: number;

  /**
   * Thời gian thực thi
   */
  @ApiProperty({
    description: 'Thời gian thực thi',
    example: 1640995200000,
  })
  executedAt: number;

  /**
   * Thời gian hoàn thành
   */
  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành',
    example: 1640995202000,
  })
  completedAt?: number;

  /**
   * Thời gian tạo log
   */
  @ApiProperty({
    description: 'Thời gian tạo log',
    example: 1640995200000,
  })
  createdAt: number;
}

/**
 * DTO response cho danh sách execution logs với phân trang
 */
export class ExecutionLogListResponseDto {
  /**
   * Danh sách execution logs
   */
  @ApiProperty({
    description: 'Danh sách execution logs',
    type: [ExecutionLogResponseDto],
  })
  items: ExecutionLogResponseDto[];

  /**
   * Tổng số logs
   */
  @ApiProperty({
    description: 'Tổng số logs',
    example: 50,
  })
  total: number;

  /**
   * Trang hiện tại
   */
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  page: number;

  /**
   * Số items per page
   */
  @ApiProperty({
    description: 'Số items per page',
    example: 10,
  })
  limit: number;

  /**
   * Tổng số trang
   */
  @ApiProperty({
    description: 'Tổng số trang',
    example: 5,
  })
  totalPages: number;
}
