import { Injectable, Logger } from '@nestjs/common';
import { DeploymentService } from '../services/deployment.service';
import { DeploymentHistoryRepository } from '../repositories/deployment-history.repository';
import { AnyDeploymentConfig } from '../interfaces/deployment-config.interface';

/**
 * Tool để thực hiện deployment automation thông qua chat interface
 */
@Injectable()
export class DeploymentAutomationTool {
  private readonly logger = new Logger(DeploymentAutomationTool.name);

  constructor(
    private readonly deploymentService: DeploymentService,
    private readonly deploymentHistoryRepository: DeploymentHistoryRepository,
  ) {}

  /**
   * Deploy dự án
   */
  async deployProject(params: {
    sshServerId: number;
    projectName: string;
    deploymentType: 'git_pull' | 'git_clone' | 'docker_build' | 'npm_build' | 'custom';
    environment: 'development' | 'staging' | 'production' | 'testing';
    projectPath: string;
    repositoryUrl?: string;
    branch?: string;
    commitHash?: string;
    tag?: string;
    buildScript?: string;
    postDeployScript?: string;
    environmentVariables?: Record<string, string>;
    additionalConfig?: Record<string, any>;
    customCommands?: Array<{
      command: string;
      description?: string;
      workingDirectory?: string;
      environment?: Record<string, string>;
      continueOnError?: boolean;
    }>;
  }, deployedBy: number) {
    try {
      const deploymentConfig: AnyDeploymentConfig = {
        projectName: params.projectName,
        deploymentType: params.deploymentType as any,
        environment: params.environment,
        projectPath: params.projectPath,
        repositoryUrl: params.repositoryUrl,
        branch: params.branch,
        commitHash: params.commitHash,
        tag: params.tag,
        buildScript: params.buildScript,
        postDeployScript: params.postDeployScript,
        environmentVariables: params.environmentVariables,
        additionalConfig: params.additionalConfig,
      };

      // Thêm custom commands nếu có
      if (params.deploymentType === 'custom' && params.customCommands) {
        (deploymentConfig as any).customCommands = params.customCommands;
      }

      const result = await this.deploymentService.deploy(
        params.sshServerId,
        deploymentConfig,
        deployedBy,
      );

      return {
        success: result.status === 'completed',
        message: result.status === 'completed' 
          ? `Deployment "${params.projectName}" thành công`
          : `Deployment "${params.projectName}" ${result.status}`,
        data: {
          deploymentId: result.deploymentId,
          projectName: params.projectName,
          deploymentType: params.deploymentType,
          environment: params.environment,
          status: result.status,
          startedAt: result.startedAt,
          completedAt: result.completedAt,
          executionTime: result.completedAt ? result.completedAt - result.startedAt : undefined,
          logs: result.logs,
          errorMessage: result.errorMessage,
          executionLogIds: result.executionLogIds,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi deploy dự án: ${error.message}`);
      return {
        success: false,
        message: `Lỗi deploy dự án: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Rollback deployment
   */
  async rollbackDeployment(params: {
    sshServerId: number;
    deploymentId: number;
  }, deployedBy: number) {
    try {
      const result = await this.deploymentService.rollback(
        params.sshServerId,
        params.deploymentId,
        deployedBy,
      );

      return {
        success: result.status === 'completed',
        message: result.status === 'completed' 
          ? `Rollback deployment ${params.deploymentId} thành công`
          : `Rollback deployment ${params.deploymentId} ${result.status}`,
        data: {
          deploymentId: result.deploymentId,
          originalDeploymentId: params.deploymentId,
          status: result.status,
          startedAt: result.startedAt,
          completedAt: result.completedAt,
          executionTime: result.completedAt ? result.completedAt - result.startedAt : undefined,
          logs: result.logs,
          errorMessage: result.errorMessage,
          executionLogIds: result.executionLogIds,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi rollback deployment: ${error.message}`);
      return {
        success: false,
        message: `Lỗi rollback deployment: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy danh sách deployment history
   */
  async getDeploymentHistory(params: {
    page?: number;
    limit?: number;
    sshServerId?: number;
    projectName?: string;
    status?: string;
    deploymentType?: string;
    environment?: string;
    search?: string;
  }, deployedBy?: number) {
    try {
      const { items, total } = await this.deploymentHistoryRepository.findWithPagination({
        page: params.page || 1,
        limit: params.limit || 10,
        sshServerId: params.sshServerId,
        projectName: params.projectName,
        status: params.status,
        deploymentType: params.deploymentType,
        environment: params.environment,
        search: params.search,
        deployedBy,
      });

      return {
        success: true,
        message: `Tìm thấy ${total} deployment histories`,
        data: {
          deployments: items.map(deployment => ({
            id: deployment.id,
            sshServerId: deployment.sshServerId,
            projectName: deployment.projectName,
            repositoryUrl: deployment.repositoryUrl,
            branch: deployment.branch,
            commitHash: deployment.commitHash,
            tag: deployment.tag,
            deploymentType: deployment.deploymentType,
            environment: deployment.environment,
            status: deployment.status,
            projectPath: deployment.projectPath,
            deployedBy: deployment.deployedBy,
            startedAt: deployment.startedAt,
            completedAt: deployment.completedAt,
            executionTime: deployment.completedAt ? deployment.completedAt - deployment.startedAt : undefined,
            errorMessage: deployment.errorMessage,
          })),
          total,
          page: params.page || 1,
          limit: params.limit || 10,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy deployment history: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy deployment history: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy deployment theo ID
   */
  async getDeploymentById(deploymentId: number) {
    try {
      const deployment = await this.deploymentHistoryRepository.findById(deploymentId);

      if (!deployment) {
        return {
          success: false,
          message: `Deployment với ID ${deploymentId} không tồn tại`,
          data: null,
        };
      }

      return {
        success: true,
        message: `Thông tin deployment ${deploymentId}`,
        data: {
          id: deployment.id,
          sshServerId: deployment.sshServerId,
          projectName: deployment.projectName,
          repositoryUrl: deployment.repositoryUrl,
          branch: deployment.branch,
          commitHash: deployment.commitHash,
          tag: deployment.tag,
          deploymentType: deployment.deploymentType,
          environment: deployment.environment,
          status: deployment.status,
          projectPath: deployment.projectPath,
          buildScript: deployment.buildScript,
          postDeployScript: deployment.postDeployScript,
          deploymentConfig: deployment.deploymentConfig ? JSON.parse(deployment.deploymentConfig) : null,
          logs: deployment.logs,
          errorMessage: deployment.errorMessage,
          jobId: deployment.jobId,
          previousDeploymentId: deployment.previousDeploymentId,
          metadata: deployment.metadata ? JSON.parse(deployment.metadata) : null,
          deployedBy: deployment.deployedBy,
          startedAt: deployment.startedAt,
          completedAt: deployment.completedAt,
          executionTime: deployment.completedAt ? deployment.completedAt - deployment.startedAt : undefined,
          createdAt: deployment.createdAt,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy deployment: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy deployment: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy deployment gần đây của dự án
   */
  async getRecentDeployments(projectName: string, limit: number = 5) {
    try {
      const deployments = await this.deploymentHistoryRepository.findRecentByProject(projectName, limit);

      return {
        success: true,
        message: `${deployments.length} deployment gần đây của dự án "${projectName}"`,
        data: {
          projectName,
          deployments: deployments.map(deployment => ({
            id: deployment.id,
            branch: deployment.branch,
            commitHash: deployment.commitHash,
            tag: deployment.tag,
            deploymentType: deployment.deploymentType,
            environment: deployment.environment,
            status: deployment.status,
            deployedBy: deployment.deployedBy,
            startedAt: deployment.startedAt,
            completedAt: deployment.completedAt,
            executionTime: deployment.completedAt ? deployment.completedAt - deployment.startedAt : undefined,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy deployment gần đây: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy deployment gần đây: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy deployment thành công cuối cùng
   */
  async getLastSuccessfulDeployment(projectName: string, environment?: string) {
    try {
      const deployment = await this.deploymentHistoryRepository.findLastSuccessfulDeployment(
        projectName,
        environment,
      );

      if (!deployment) {
        return {
          success: false,
          message: `Không tìm thấy deployment thành công cho dự án "${projectName}"`,
          data: null,
        };
      }

      return {
        success: true,
        message: `Deployment thành công cuối cùng của dự án "${projectName}"`,
        data: {
          id: deployment.id,
          projectName: deployment.projectName,
          branch: deployment.branch,
          commitHash: deployment.commitHash,
          tag: deployment.tag,
          deploymentType: deployment.deploymentType,
          environment: deployment.environment,
          status: deployment.status,
          deployedBy: deployment.deployedBy,
          startedAt: deployment.startedAt,
          completedAt: deployment.completedAt,
          executionTime: deployment.completedAt ? deployment.completedAt - deployment.startedAt : undefined,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy deployment thành công cuối: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy deployment thành công cuối: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy thống kê deployment theo dự án
   */
  async getDeploymentStatistics(projectName: string, fromDate?: number, toDate?: number) {
    try {
      const statistics = await this.deploymentHistoryRepository.getStatisticsByProject(
        projectName,
        fromDate,
        toDate,
      );

      return {
        success: true,
        message: `Thống kê deployment cho dự án "${projectName}"`,
        data: {
          projectName,
          ...statistics,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy thống kê deployment: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy thống kê deployment: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy danh sách deployment đang chạy
   */
  async getRunningDeployments() {
    try {
      const deployments = await this.deploymentHistoryRepository.findRunningDeployments();

      return {
        success: true,
        message: `Tìm thấy ${deployments.length} deployment đang chạy`,
        data: {
          deployments: deployments.map(deployment => ({
            id: deployment.id,
            projectName: deployment.projectName,
            deploymentType: deployment.deploymentType,
            environment: deployment.environment,
            status: deployment.status,
            deployedBy: deployment.deployedBy,
            startedAt: deployment.startedAt,
            runningTime: Date.now() - deployment.startedAt,
          })),
          total: deployments.length,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy deployment đang chạy: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy deployment đang chạy: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Validate cấu hình deployment
   */
  async validateDeploymentConfig(config: AnyDeploymentConfig) {
    try {
      const validation = await this.deploymentService.validateDeploymentConfig(config);

      return {
        success: validation.isValid,
        message: validation.isValid 
          ? 'Cấu hình deployment hợp lệ'
          : `Cấu hình deployment không hợp lệ: ${validation.errors.join(', ')}`,
        data: {
          isValid: validation.isValid,
          errors: validation.errors,
          warnings: validation.warnings,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi validate deployment config: ${error.message}`);
      return {
        success: false,
        message: `Lỗi validate deployment config: ${error.message}`,
        data: null,
      };
    }
  }
}
