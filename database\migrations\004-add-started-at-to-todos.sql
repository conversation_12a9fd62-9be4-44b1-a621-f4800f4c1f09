-- =====================================================
-- MIGRATION: Add started_at column to todos table
-- Ngày: 2025-06-19
-- M<PERSON> tả: Thêm cột started_at để theo dõi thời gian bắt đầu làm việc
-- =====================================================

-- Bắt đầu transaction
BEGIN;

-- Tạo bảng migration tracking nếu chưa có
CREATE TABLE IF NOT EXISTS migration_history (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'running',
    started_at BIGINT DEFAULT EXTRACT(epoch FROM now()) * 1000,
    completed_at BIGINT,
    error_message TEXT
);

-- <PERSON><PERSON> log bắt đầu migration
INSERT INTO migration_history (migration_name) VALUES ('004-add-started-at-to-todos');

DO $$
DECLARE
    migration_id INTEGER;
BEGIN
    -- Lấy ID của migration vừa tạo
    SELECT id INTO migration_id FROM migration_history 
    WHERE migration_name = '004-add-started-at-to-todos' 
    ORDER BY started_at DESC LIMIT 1;

    RAISE NOTICE 'Starting Migration 004: Add started_at column to todos table';
    
    -- Kiểm tra xem cột started_at đã tồn tại chưa
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'todos' 
        AND column_name = 'started_at'
    ) THEN
        -- Thêm cột started_at vào bảng todos
        ALTER TABLE todos ADD COLUMN started_at BIGINT NULL;
        
        RAISE NOTICE 'Added started_at column to todos table';
        
        -- Tạo index cho cột started_at để tối ưu query
        CREATE INDEX IF NOT EXISTS idx_todos_started_at ON todos(started_at);
        
        RAISE NOTICE 'Created index on started_at column';
        
        -- Cập nhật started_at cho các todo đã có status IN_PROGRESS
        -- Sử dụng updated_at làm giá trị mặc định
        UPDATE todos 
        SET started_at = updated_at 
        WHERE status = 'in_progress' 
        AND started_at IS NULL 
        AND updated_at IS NOT NULL;
        
        RAISE NOTICE 'Updated started_at for existing IN_PROGRESS todos';
        
    ELSE
        RAISE NOTICE 'Column started_at already exists in todos table';
    END IF;
    
    -- Cập nhật trạng thái migration thành công
    UPDATE migration_history 
    SET status = 'completed', 
        completed_at = EXTRACT(epoch FROM now()) * 1000
    WHERE id = migration_id;
    
    RAISE NOTICE 'Migration 004 completed successfully';
    
EXCEPTION
    WHEN OTHERS THEN
        -- Cập nhật trạng thái migration thất bại
        UPDATE migration_history 
        SET status = 'failed', 
            completed_at = EXTRACT(epoch FROM now()) * 1000,
            error_message = SQLERRM
        WHERE id = migration_id;
        
        RAISE EXCEPTION 'Migration 004 failed: %', SQLERRM;
END $$;

-- Kiểm tra kết quả migration
SELECT 
    'MIGRATION 004 VERIFICATION' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'todos' AND column_name = 'started_at'
        ) THEN 'SUCCESS: started_at column exists'
        ELSE 'FAILED: started_at column not found'
    END as column_check,
    (SELECT COUNT(*) FROM todos WHERE started_at IS NOT NULL) as todos_with_started_at,
    (SELECT COUNT(*) FROM todos WHERE status = 'in_progress') as in_progress_todos;

-- Hiển thị thông tin migration
SELECT * FROM migration_history WHERE migration_name = '004-add-started-at-to-todos';

COMMIT;

-- Thông báo hoàn thành
SELECT 'MIGRATION 004: ADD STARTED_AT TO TODOS COMPLETED SUCCESSFULLY' as status;
