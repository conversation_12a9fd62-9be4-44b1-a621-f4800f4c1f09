/**
 * Interface cho cấu hình deployment
 */
export interface DeploymentConfig {
  /** Tên dự án */
  projectName: string;
  /** URL repository */
  repositoryUrl?: string;
  /** Branch để deploy */
  branch?: string;
  /** Commit hash cụ thể */
  commitHash?: string;
  /** Tag version */
  tag?: string;
  /** Loại deployment */
  deploymentType: 'git_pull' | 'git_clone' | 'docker_build' | 'npm_build' | 'custom' | 'rollback';
  /** Môi trường deployment */
  environment: 'development' | 'staging' | 'production' | 'testing';
  /** Thư mục dự án trên server */
  projectPath: string;
  /** Script build tùy chỉnh */
  buildScript?: string;
  /** Script post-deployment */
  postDeployScript?: string;
  /** Biến môi trường */
  environmentVariables?: Record<string, string>;
  /** C<PERSON><PERSON> h<PERSON><PERSON> bổ sung */
  additionalConfig?: Record<string, any>;
}

/**
 * Interface cho kết quả deployment
 */
export interface DeploymentResult {
  /** ID deployment history */
  deploymentId: number;
  /** Trạng thái deployment */
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'rollback';
  /** Thời gian bắt đầu */
  startedAt: number;
  /** Thời gian hoàn thành */
  completedAt?: number;
  /** Logs deployment */
  logs: string;
  /** Thông báo lỗi */
  errorMessage?: string;
  /** Metadata */
  metadata?: Record<string, any>;
  /** Danh sách log IDs của các lệnh */
  executionLogIds: number[];
}

/**
 * Interface cho Git deployment config
 */
export interface GitDeploymentConfig extends DeploymentConfig {
  deploymentType: 'git_pull' | 'git_clone';
  /** Có backup trước khi deploy không */
  createBackup?: boolean;
  /** Có stash changes không */
  stashChanges?: boolean;
  /** Có force pull không */
  forcePull?: boolean;
}

/**
 * Interface cho Docker deployment config
 */
export interface DockerDeploymentConfig extends DeploymentConfig {
  deploymentType: 'docker_build';
  /** Dockerfile path */
  dockerfilePath?: string;
  /** Docker image tag */
  imageTag?: string;
  /** Docker compose file */
  composeFile?: string;
  /** Container name */
  containerName?: string;
  /** Port mapping */
  portMapping?: Record<string, string>;
  /** Volume mapping */
  volumeMapping?: Record<string, string>;
}

/**
 * Interface cho NPM deployment config
 */
export interface NpmDeploymentConfig extends DeploymentConfig {
  deploymentType: 'npm_build';
  /** Package manager (npm, yarn, pnpm) */
  packageManager?: 'npm' | 'yarn' | 'pnpm';
  /** Build command */
  buildCommand?: string;
  /** Start command */
  startCommand?: string;
  /** PM2 ecosystem file */
  pm2EcosystemFile?: string;
  /** PM2 app name */
  pm2AppName?: string;
}

/**
 * Interface cho Custom deployment config
 */
export interface CustomDeploymentConfig extends DeploymentConfig {
  deploymentType: 'custom';
  /** Danh sách lệnh tùy chỉnh */
  customCommands: Array<{
    command: string;
    description?: string;
    workingDirectory?: string;
    environment?: Record<string, string>;
    continueOnError?: boolean;
  }>;
}

/**
 * Interface cho Rollback deployment config
 */
export interface RollbackDeploymentConfig extends DeploymentConfig {
  deploymentType: 'rollback';
  /** ID deployment trước đó để rollback */
  previousDeploymentId: number;
  /** Có tạo backup trước rollback không */
  createBackupBeforeRollback?: boolean;
}

/**
 * Union type cho tất cả deployment configs
 */
export type AnyDeploymentConfig = 
  | GitDeploymentConfig 
  | DockerDeploymentConfig 
  | NpmDeploymentConfig 
  | CustomDeploymentConfig 
  | RollbackDeploymentConfig;

/**
 * Interface cho deployment status update
 */
export interface DeploymentStatusUpdate {
  /** ID deployment */
  deploymentId: number;
  /** Trạng thái mới */
  status: DeploymentResult['status'];
  /** Logs bổ sung */
  additionalLogs?: string;
  /** Thông báo lỗi */
  errorMessage?: string;
  /** Metadata bổ sung */
  metadata?: Record<string, any>;
}

/**
 * Interface cho deployment statistics
 */
export interface DeploymentStatistics {
  /** Tổng số deployment */
  totalDeployments: number;
  /** Số deployment thành công */
  successfulDeployments: number;
  /** Số deployment thất bại */
  failedDeployments: number;
  /** Tỷ lệ thành công (%) */
  successRate: number;
  /** Thời gian deployment trung bình (ms) */
  averageDeploymentTime: number;
  /** Deployment gần đây nhất */
  lastDeployment?: {
    id: number;
    projectName: string;
    status: string;
    deployedAt: number;
  };
}

/**
 * Interface cho deployment validation
 */
export interface DeploymentValidation {
  /** Validation có hợp lệ không */
  isValid: boolean;
  /** Danh sách lỗi validation */
  errors: string[];
  /** Danh sách cảnh báo */
  warnings: string[];
  /** Thông tin bổ sung */
  info?: Record<string, any>;
}
