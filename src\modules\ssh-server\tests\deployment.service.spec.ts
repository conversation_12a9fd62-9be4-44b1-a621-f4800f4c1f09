import { Test, TestingModule } from '@nestjs/testing';
import { DeploymentService } from '../services/deployment.service';
import { SshCommandService } from '../services/ssh-command.service';
import { DeploymentHistoryRepository } from '../repositories/deployment-history.repository';
import { SshServerRepository } from '../repositories/ssh-server.repository';
import { GitDeploymentConfig, DockerDeploymentConfig } from '../interfaces/deployment-config.interface';

describe('DeploymentService', () => {
  let service: DeploymentService;
  let sshCommandService: jest.Mocked<SshCommandService>;
  let deploymentHistoryRepository: jest.Mocked<DeploymentHistoryRepository>;
  let sshServerRepository: jest.Mocked<SshServerRepository>;

  const mockDeployment = {
    id: 1,
    sshServerId: 1,
    projectName: 'test-project',
    deploymentType: 'git_pull',
    environment: 'production',
    projectPath: '/home/<USER>/test-project',
    status: 'pending',
    startedAt: Date.now(),
    deployedBy: 1,
  };

  const mockBatchResult = {
    totalCommands: 3,
    successCount: 3,
    failureCount: 0,
    results: [
      {
        command: 'git pull',
        stdout: 'Already up to date.',
        stderr: '',
        exitCode: 0,
        executionTime: 1000,
        success: true,
      },
      {
        command: 'npm install',
        stdout: 'added 150 packages',
        stderr: '',
        exitCode: 0,
        executionTime: 5000,
        success: true,
      },
      {
        command: 'npm run build',
        stdout: 'Build completed successfully',
        stderr: '',
        exitCode: 0,
        executionTime: 10000,
        success: true,
      },
    ],
    totalExecutionTime: 16000,
    overallSuccess: true,
  };

  beforeEach(async () => {
    const mockSshCommandService = {
      executeBatchCommands: jest.fn(),
    };

    const mockDeploymentHistoryRepo = {
      create: jest.fn(),
      updateStatus: jest.fn(),
      findById: jest.fn(),
      findLastSuccessfulDeployment: jest.fn(),
      getStatisticsByProject: jest.fn(),
    };

    const mockSshServerRepo = {
      findById: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeploymentService,
        {
          provide: SshCommandService,
          useValue: mockSshCommandService,
        },
        {
          provide: DeploymentHistoryRepository,
          useValue: mockDeploymentHistoryRepo,
        },
        {
          provide: SshServerRepository,
          useValue: mockSshServerRepo,
        },
      ],
    }).compile();

    service = module.get<DeploymentService>(DeploymentService);
    sshCommandService = module.get(SshCommandService);
    deploymentHistoryRepository = module.get(DeploymentHistoryRepository);
    sshServerRepository = module.get(SshServerRepository);
  });

  describe('deploy', () => {
    const gitDeploymentConfig: GitDeploymentConfig = {
      projectName: 'test-project',
      deploymentType: 'git_pull',
      environment: 'production',
      projectPath: '/home/<USER>/test-project',
      branch: 'main',
      buildScript: 'npm run build',
      postDeployScript: 'pm2 restart app',
    };

    it('should deploy project successfully', async () => {
      deploymentHistoryRepository.create.mockResolvedValue(mockDeployment);
      deploymentHistoryRepository.updateStatus.mockResolvedValue(undefined);
      sshCommandService.executeBatchCommands.mockResolvedValue({
        result: mockBatchResult,
        logIds: [1, 2, 3],
      });

      const result = await service.deploy(1, gitDeploymentConfig, 1);

      expect(result.status).toBe('completed');
      expect(result.deploymentId).toBe(1);
      expect(result.executionLogIds).toEqual([1, 2, 3]);

      expect(deploymentHistoryRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          projectName: 'test-project',
          deploymentType: 'git_pull',
          environment: 'production',
          status: 'pending',
          deployedBy: 1,
        })
      );

      expect(deploymentHistoryRepository.updateStatus).toHaveBeenCalledWith(
        1,
        'running'
      );

      expect(deploymentHistoryRepository.updateStatus).toHaveBeenCalledWith(
        1,
        'completed',
        expect.any(String),
        undefined,
        expect.any(String)
      );
    });

    it('should handle deployment failure', async () => {
      const failedBatchResult = {
        ...mockBatchResult,
        successCount: 2,
        failureCount: 1,
        overallSuccess: false,
        results: [
          ...mockBatchResult.results.slice(0, 2),
          {
            command: 'npm run build',
            stdout: '',
            stderr: 'Build failed: syntax error',
            exitCode: 1,
            executionTime: 2000,
            success: false,
            error: 'Build process failed',
          },
        ],
      };

      deploymentHistoryRepository.create.mockResolvedValue(mockDeployment);
      sshCommandService.executeBatchCommands.mockResolvedValue({
        result: failedBatchResult,
        logIds: [1, 2, 3],
      });

      const result = await service.deploy(1, gitDeploymentConfig, 1);

      expect(result.status).toBe('failed');
      expect(result.errorMessage).toContain('Build process failed');

      expect(deploymentHistoryRepository.updateStatus).toHaveBeenCalledWith(
        1,
        'failed',
        expect.any(String),
        expect.stringContaining('Build process failed'),
        expect.any(String)
      );
    });

    it('should handle deployment exception', async () => {
      deploymentHistoryRepository.create.mockResolvedValue(mockDeployment);
      sshCommandService.executeBatchCommands.mockRejectedValue(
        new Error('SSH connection failed')
      );

      const result = await service.deploy(1, gitDeploymentConfig, 1);

      expect(result.status).toBe('failed');
      expect(result.errorMessage).toBe('SSH connection failed');

      expect(deploymentHistoryRepository.updateStatus).toHaveBeenCalledWith(
        1,
        'failed',
        '',
        'SSH connection failed'
      );
    });
  });

  describe('rollback', () => {
    const targetDeployment = {
      ...mockDeployment,
      id: 5,
      status: 'completed',
      projectName: 'test-project',
      environment: 'production',
    };

    const previousDeployment = {
      ...mockDeployment,
      id: 3,
      status: 'completed',
      commitHash: 'abc123',
    };

    it('should rollback deployment successfully', async () => {
      deploymentHistoryRepository.findById.mockResolvedValue(targetDeployment);
      deploymentHistoryRepository.findLastSuccessfulDeployment.mockResolvedValue(previousDeployment);
      deploymentHistoryRepository.create.mockResolvedValue({
        ...mockDeployment,
        id: 6,
        deploymentType: 'rollback',
      });
      sshCommandService.executeBatchCommands.mockResolvedValue({
        result: mockBatchResult,
        logIds: [7, 8, 9],
      });

      const result = await service.rollback(1, 5, 1);

      expect(result.status).toBe('completed');
      expect(result.deploymentId).toBe(6);

      expect(deploymentHistoryRepository.findById).toHaveBeenCalledWith(5);
      expect(deploymentHistoryRepository.findLastSuccessfulDeployment).toHaveBeenCalledWith(
        'test-project',
        'production'
      );
    });

    it('should handle rollback of non-completed deployment', async () => {
      const failedDeployment = { ...targetDeployment, status: 'failed' };
      deploymentHistoryRepository.findById.mockResolvedValue(failedDeployment);

      await expect(service.rollback(1, 5, 1)).rejects.toThrow(
        'Chỉ có thể rollback deployment đã hoàn thành thành công'
      );
    });

    it('should handle no previous deployment found', async () => {
      deploymentHistoryRepository.findById.mockResolvedValue(targetDeployment);
      deploymentHistoryRepository.findLastSuccessfulDeployment.mockResolvedValue(null);

      await expect(service.rollback(1, 5, 1)).rejects.toThrow(
        'Không tìm thấy deployment trước đó để rollback'
      );
    });

    it('should handle rollback to same deployment', async () => {
      deploymentHistoryRepository.findById.mockResolvedValue(targetDeployment);
      deploymentHistoryRepository.findLastSuccessfulDeployment.mockResolvedValue(targetDeployment);

      await expect(service.rollback(1, 5, 1)).rejects.toThrow(
        'Không tìm thấy deployment trước đó để rollback'
      );
    });
  });

  describe('validateDeploymentConfig', () => {
    it('should validate git deployment config successfully', async () => {
      const validConfig: GitDeploymentConfig = {
        projectName: 'test-project',
        deploymentType: 'git_pull',
        environment: 'production',
        projectPath: '/home/<USER>/test-project',
        branch: 'main',
      };

      const result = await service.validateDeploymentConfig(validConfig);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate docker deployment config successfully', async () => {
      const validConfig: DockerDeploymentConfig = {
        projectName: 'docker-app',
        deploymentType: 'docker_build',
        environment: 'production',
        projectPath: '/home/<USER>/docker-app',
        composeFile: 'docker-compose.yml',
      };

      const result = await service.validateDeploymentConfig(validConfig);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return validation errors for invalid config', async () => {
      const invalidConfig = {
        projectName: '', // Empty project name
        deploymentType: 'git_pull',
        environment: 'production',
        projectPath: '', // Empty project path
      } as any;

      const result = await service.validateDeploymentConfig(invalidConfig);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Tên dự án không được để trống');
      expect(result.errors).toContain('Đường dẫn dự án không được để trống');
    });

    it('should validate git clone requires repository URL', async () => {
      const invalidConfig: GitDeploymentConfig = {
        projectName: 'test-project',
        deploymentType: 'git_clone',
        environment: 'production',
        projectPath: '/home/<USER>/test-project',
        // Missing repositoryUrl
      };

      const result = await service.validateDeploymentConfig(invalidConfig);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Repository URL là bắt buộc cho git clone');
    });
  });

  describe('getDeploymentStatistics', () => {
    it('should return deployment statistics for project', async () => {
      const mockStats = {
        totalDeployments: 10,
        successfulDeployments: 8,
        failedDeployments: 2,
        successRate: 80,
        averageDeploymentTime: 120000,
        lastDeployment: {
          id: 10,
          projectName: 'test-project',
          status: 'completed',
          deployedAt: Date.now(),
        },
      };

      deploymentHistoryRepository.getStatisticsByProject.mockResolvedValue(mockStats);

      const result = await service.getDeploymentStatistics('test-project');

      expect(result).toEqual(mockStats);
      expect(deploymentHistoryRepository.getStatisticsByProject).toHaveBeenCalledWith(
        'test-project',
        undefined,
        undefined
      );
    });

    it('should return deployment statistics with date range', async () => {
      const fromDate = Date.now() - 30 * 24 * 60 * 60 * 1000; // 30 days ago
      const toDate = Date.now();

      await service.getDeploymentStatistics('test-project', fromDate, toDate);

      expect(deploymentHistoryRepository.getStatisticsByProject).toHaveBeenCalledWith(
        'test-project',
        fromDate,
        toDate
      );
    });
  });
});
