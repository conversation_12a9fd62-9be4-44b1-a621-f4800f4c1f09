import { Injectable, Logger } from '@nestjs/common';
import { SshConnectionService } from './ssh-connection.service';
import { SshExecutionLogRepository } from '../repositories/ssh-execution-log.repository';
import { SshServerRepository } from '../repositories/ssh-server.repository';
import { 
  SshCommandResult, 
  SshBatchCommand, 
  SshBatchResult,
  SshExecutionOptions,
} from '../interfaces/ssh-connection.interface';
import { SshExecutionLog } from '../entities/ssh-execution-log.entity';
import { SSH_COMMANDS, DEPLOYMENT_TEMPLATES } from '../constants/ssh-commands.constants';

/**
 * Service thực thi các lệnh SSH với logging và queue support
 */
@Injectable()
export class SshCommandService {
  private readonly logger = new Logger(SshCommandService.name);

  constructor(
    private readonly sshConnectionService: SshConnectionService,
    private readonly sshExecutionLogRepository: SshExecutionLogRepository,
    private readonly sshServerRepository: SshServerRepository,
  ) {}

  /**
   * Thực thi lệnh SSH với logging
   * @param sshServerId ID server SSH
   * @param command Lệnh cần thực thi
   * @param executedBy ID người thực thi
   * @param options Tùy chọn thực thi
   * @returns Kết quả thực thi và log ID
   */
  async executeCommand(
    sshServerId: number,
    command: string,
    executedBy: number,
    options: SshExecutionOptions & {
      executionType?: SshExecutionLog['executionType'];
      jobId?: string;
      metadata?: any;
    } = {},
  ): Promise<{ result: SshCommandResult; logId: number }> {
    // Tạo log thực thi
    const executionLog = await this.sshExecutionLogRepository.create({
      sshServerId,
      command,
      workingDirectory: options.workingDirectory,
      environmentVariables: options.environment ? JSON.stringify(options.environment) : null,
      status: 'pending',
      executionType: options.executionType || 'manual',
      jobId: options.jobId,
      metadata: options.metadata ? JSON.stringify(options.metadata) : null,
      executedBy,
    });

    try {
      // Lấy thông tin server SSH
      const sshServer = await this.sshServerRepository.findById(sshServerId);
      if (!sshServer) {
        throw new Error(`SSH server với ID ${sshServerId} không tồn tại`);
      }

      if (!sshServer.isActive) {
        throw new Error(`SSH server ${sshServer.name} đã bị vô hiệu hóa`);
      }

      // Tạo kết nối SSH
      const config = await this.sshConnectionService.createConnectionConfig(sshServer);
      const connectionId = await this.sshConnectionService.createConnection(config);

      // Cập nhật trạng thái đang chạy
      await this.sshExecutionLogRepository.updateStatus(executionLog.id, 'running');

      try {
        // Thực thi lệnh
        const result = await this.sshConnectionService.executeCommand(
          connectionId,
          command,
          options,
        );

        // Cập nhật kết quả
        await this.sshExecutionLogRepository.updateStatus(
          executionLog.id,
          result.success ? 'completed' : 'failed',
          result.stdout,
          result.stderr,
          result.exitCode,
          result.executionTime,
        );

        // Cập nhật trạng thái kết nối server
        await this.sshServerRepository.updateConnectionStatus(
          sshServerId,
          'connected',
        );

        this.logger.log(`Thực thi lệnh SSH thành công trên server ${sshServer.name}: ${command}`);

        return { result, logId: executionLog.id };

      } finally {
        // Đóng kết nối
        await this.sshConnectionService.closeConnection(connectionId);
      }

    } catch (error) {
      // Cập nhật lỗi
      await this.sshExecutionLogRepository.updateStatus(
        executionLog.id,
        'failed',
        '',
        error.message,
        -1,
      );

      // Cập nhật trạng thái kết nối server
      await this.sshServerRepository.updateConnectionStatus(
        sshServerId,
        'error',
        error.message,
      );

      this.logger.error(`Lỗi thực thi lệnh SSH: ${error.message}`);
      
      const failedResult: SshCommandResult = {
        command,
        stdout: '',
        stderr: error.message,
        exitCode: -1,
        executionTime: 0,
        success: false,
        error: error.message,
      };

      return { result: failedResult, logId: executionLog.id };
    }
  }

  /**
   * Thực thi batch commands với logging
   * @param sshServerId ID server SSH
   * @param commands Danh sách lệnh
   * @param executedBy ID người thực thi
   * @param options Tùy chọn thực thi
   * @returns Kết quả batch execution
   */
  async executeBatchCommands(
    sshServerId: number,
    commands: SshBatchCommand[],
    executedBy: number,
    options: {
      executionType?: SshExecutionLog['executionType'];
      jobId?: string;
      metadata?: any;
    } = {},
  ): Promise<{ result: SshBatchResult; logIds: number[] }> {
    const logIds: number[] = [];
    const results: Array<SshCommandResult & { description?: string }> = [];
    let successCount = 0;
    let failureCount = 0;
    const startTime = Date.now();

    this.logger.log(`Bắt đầu thực thi batch commands trên server ${sshServerId} (${commands.length} lệnh)`);

    // Lấy thông tin server SSH
    const sshServer = await this.sshServerRepository.findById(sshServerId);
    if (!sshServer) {
      throw new Error(`SSH server với ID ${sshServerId} không tồn tại`);
    }

    if (!sshServer.isActive) {
      throw new Error(`SSH server ${sshServer.name} đã bị vô hiệu hóa`);
    }

    // Tạo kết nối SSH
    const config = await this.sshConnectionService.createConnectionConfig(sshServer);
    const connectionId = await this.sshConnectionService.createConnection(config);

    try {
      for (const batchCommand of commands) {
        try {
          const { result, logId } = await this.executeCommandWithConnection(
            connectionId,
            sshServerId,
            batchCommand.command,
            executedBy,
            {
              workingDirectory: batchCommand.workingDirectory,
              environment: batchCommand.environment,
              executionType: options.executionType || 'batch',
              jobId: options.jobId,
              metadata: { 
                ...options.metadata, 
                description: batchCommand.description,
                batchIndex: logIds.length,
              },
            },
          );

          logIds.push(logId);
          results.push({
            ...result,
            description: batchCommand.description,
          });

          if (result.success) {
            successCount++;
          } else {
            failureCount++;
            
            // Dừng nếu lệnh thất bại và không được cấu hình tiếp tục
            if (!batchCommand.continueOnError) {
              this.logger.warn(`Dừng batch execution do lệnh thất bại: ${batchCommand.command}`);
              break;
            }
          }

        } catch (error) {
          failureCount++;
          
          // Tạo log cho lệnh thất bại
          const failedLog = await this.sshExecutionLogRepository.create({
            sshServerId,
            command: batchCommand.command,
            workingDirectory: batchCommand.workingDirectory,
            environmentVariables: batchCommand.environment ? JSON.stringify(batchCommand.environment) : null,
            status: 'failed',
            errorOutput: error.message,
            exitCode: -1,
            executionType: options.executionType || 'batch',
            jobId: options.jobId,
            metadata: JSON.stringify({ 
              ...options.metadata, 
              description: batchCommand.description,
              batchIndex: logIds.length,
            }),
            executedBy,
          });

          logIds.push(failedLog.id);
          results.push({
            command: batchCommand.command,
            stdout: '',
            stderr: error.message,
            exitCode: -1,
            executionTime: 0,
            success: false,
            error: error.message,
            description: batchCommand.description,
          });

          if (!batchCommand.continueOnError) {
            break;
          }
        }
      }

    } finally {
      // Đóng kết nối
      await this.sshConnectionService.closeConnection(connectionId);
    }

    const totalExecutionTime = Date.now() - startTime;
    const overallSuccess = failureCount === 0;

    // Cập nhật trạng thái kết nối server
    await this.sshServerRepository.updateConnectionStatus(
      sshServerId,
      overallSuccess ? 'connected' : 'error',
      overallSuccess ? undefined : 'Một số lệnh thất bại trong batch execution',
    );

    this.logger.log(`Hoàn thành batch execution trên server ${sshServer.name}: ${successCount}/${commands.length} thành công (${totalExecutionTime}ms)`);

    const batchResult: SshBatchResult = {
      totalCommands: commands.length,
      successCount,
      failureCount,
      results,
      totalExecutionTime,
      overallSuccess,
    };

    return { result: batchResult, logIds };
  }

  /**
   * Thực thi deployment template
   * @param sshServerId ID server SSH
   * @param templateName Tên template
   * @param executedBy ID người thực thi
   * @param customCommands Lệnh tùy chỉnh (cho template CUSTOM)
   * @param options Tùy chọn thực thi
   * @returns Kết quả deployment
   */
  async executeDeploymentTemplate(
    sshServerId: number,
    templateName: keyof typeof DEPLOYMENT_TEMPLATES,
    executedBy: number,
    customCommands?: SshBatchCommand[],
    options: {
      jobId?: string;
      metadata?: any;
    } = {},
  ): Promise<{ result: SshBatchResult; logIds: number[] }> {
    let template = DEPLOYMENT_TEMPLATES[templateName];
    
    if (templateName === 'CUSTOM' && customCommands) {
      template = {
        ...template,
        commands: customCommands.map(cmd => ({
          command: cmd.command,
          description: cmd.description || cmd.command,
        })),
      };
    }

    const batchCommands: SshBatchCommand[] = template.commands.map(cmd => ({
      command: cmd.command,
      description: cmd.description,
      continueOnError: false, // Deployment thường cần dừng khi có lỗi
    }));

    this.logger.log(`Thực thi deployment template "${template.name}" trên server ${sshServerId}`);

    return this.executeBatchCommands(
      sshServerId,
      batchCommands,
      executedBy,
      {
        executionType: 'deployment',
        jobId: options.jobId,
        metadata: {
          ...options.metadata,
          templateName,
          templateDescription: template.description,
        },
      },
    );
  }

  /**
   * Lấy các lệnh phổ biến theo loại
   * @param category Loại lệnh
   * @returns Danh sách lệnh
   */
  getCommonCommands(category: keyof typeof SSH_COMMANDS): Record<string, string | Function> {
    return SSH_COMMANDS[category];
  }

  /**
   * Lấy danh sách deployment templates
   * @returns Danh sách templates
   */
  getDeploymentTemplates(): typeof DEPLOYMENT_TEMPLATES {
    return DEPLOYMENT_TEMPLATES;
  }

  /**
   * Thực thi lệnh với kết nối đã có
   * @param connectionId ID kết nối
   * @param sshServerId ID server SSH
   * @param command Lệnh
   * @param executedBy ID người thực thi
   * @param options Tùy chọn
   * @returns Kết quả thực thi
   */
  private async executeCommandWithConnection(
    connectionId: string,
    sshServerId: number,
    command: string,
    executedBy: number,
    options: SshExecutionOptions & {
      executionType?: SshExecutionLog['executionType'];
      jobId?: string;
      metadata?: any;
    } = {},
  ): Promise<{ result: SshCommandResult; logId: number }> {
    // Tạo log thực thi
    const executionLog = await this.sshExecutionLogRepository.create({
      sshServerId,
      command,
      workingDirectory: options.workingDirectory,
      environmentVariables: options.environment ? JSON.stringify(options.environment) : null,
      status: 'running',
      executionType: options.executionType || 'manual',
      jobId: options.jobId,
      metadata: options.metadata ? JSON.stringify(options.metadata) : null,
      executedBy,
    });

    try {
      // Thực thi lệnh
      const result = await this.sshConnectionService.executeCommand(
        connectionId,
        command,
        options,
      );

      // Cập nhật kết quả
      await this.sshExecutionLogRepository.updateStatus(
        executionLog.id,
        result.success ? 'completed' : 'failed',
        result.stdout,
        result.stderr,
        result.exitCode,
        result.executionTime,
      );

      return { result, logId: executionLog.id };

    } catch (error) {
      // Cập nhật lỗi
      await this.sshExecutionLogRepository.updateStatus(
        executionLog.id,
        'failed',
        '',
        error.message,
        -1,
      );

      const failedResult: SshCommandResult = {
        command,
        stdout: '',
        stderr: error.message,
        exitCode: -1,
        executionTime: 0,
        success: false,
        error: error.message,
      };

      return { result: failedResult, logId: executionLog.id };
    }
  }
}
