import { Injectable, Logger } from '@nestjs/common';
import { EncryptionService } from '@/shared/services/encryption.service';
import { SshServerRepository } from '../repositories/ssh-server.repository';
import { SshConnectionService } from './ssh-connection.service';
import { SshServer } from '../entities/ssh-server.entity';
import { CreateSshServerDto } from '../dtos/requests/create-ssh-server.dto';
import { UpdateSshServerDto } from '../dtos/requests/update-ssh-server.dto';
import { AppException, ErrorCode } from '@/common';

/**
 * Service quản lý SSH servers
 */
@Injectable()
export class SshServerService {
  private readonly logger = new Logger(SshServerService.name);

  constructor(
    private readonly sshServerRepository: SshServerRepository,
    private readonly sshConnectionService: SshConnectionService,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Tạo SSH server mới
   * @param createDto Dữ liệu tạo SSH server
   * @param createdBy ID người tạo
   * @returns SSH server đã tạo
   */
  async create(createDto: CreateSshServerDto, createdBy: number): Promise<SshServer> {
    // Kiểm tra tên server đã tồn tại
    const existingServer = await this.sshServerRepository.findByName(createDto.name);
    if (existingServer) {
      throw new AppException(
        ErrorCode.CONFLICT,
        `SSH server với tên "${createDto.name}" đã tồn tại`,
      );
    }

    // Validate credentials
    if (!createDto.password && !createDto.privateKey) {
      throw new AppException(
        ErrorCode.BAD_REQUEST,
        'Cần cung cấp password hoặc private key',
      );
    }

    // Mã hóa credentials
    const serverData: Partial<SshServer> = {
      name: createDto.name,
      host: createDto.host,
      port: createDto.port || 22,
      username: createDto.username,
      description: createDto.description,
      connectionTimeout: createDto.connectionTimeout || 30000,
      commandTimeout: createDto.commandTimeout || 300000,
      retryAttempts: createDto.retryAttempts || 3,
      retryDelay: createDto.retryDelay || 5000,
      isActive: createDto.isActive !== false,
      createdBy,
    };

    // Mã hóa password nếu có
    if (createDto.password) {
      serverData.passwordEncrypted = await this.encryptionService.encrypt(createDto.password);
    }

    // Mã hóa private key nếu có
    if (createDto.privateKey) {
      serverData.privateKeyEncrypted = await this.encryptionService.encrypt(createDto.privateKey);
    }

    // Mã hóa passphrase nếu có
    if (createDto.passphrase) {
      serverData.passphraseEncrypted = await this.encryptionService.encrypt(createDto.passphrase);
    }

    const server = await this.sshServerRepository.create(serverData);
    
    this.logger.log(`Đã tạo SSH server mới: ${server.name} (ID: ${server.id})`);
    
    return server;
  }

  /**
   * Lấy SSH server theo ID
   * @param id ID SSH server
   * @returns SSH server hoặc null
   */
  async findById(id: number): Promise<SshServer | null> {
    return this.sshServerRepository.findById(id);
  }

  /**
   * Lấy SSH server theo ID với validation
   * @param id ID SSH server
   * @returns SSH server
   */
  async findByIdOrThrow(id: number): Promise<SshServer> {
    const server = await this.findById(id);
    if (!server) {
      throw new AppException(
        ErrorCode.NOT_FOUND,
        `SSH server với ID ${id} không tồn tại`,
      );
    }
    return server;
  }

  /**
   * Lấy danh sách SSH servers với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách SSH servers và tổng số
   */
  async findWithPagination(options: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<{ items: SshServer[]; total: number }> {
    return this.sshServerRepository.findWithPagination(options);
  }

  /**
   * Lấy tất cả SSH servers đang hoạt động
   * @returns Danh sách SSH servers đang hoạt động
   */
  async findAllActive(): Promise<SshServer[]> {
    return this.sshServerRepository.findAllActive();
  }

  /**
   * Cập nhật SSH server
   * @param id ID SSH server
   * @param updateDto Dữ liệu cập nhật
   * @param updatedBy ID người cập nhật
   * @returns SSH server đã cập nhật
   */
  async update(id: number, updateDto: UpdateSshServerDto, updatedBy: number): Promise<SshServer> {
    const server = await this.findByIdOrThrow(id);

    // Kiểm tra tên server đã tồn tại (nếu thay đổi tên)
    if (updateDto.name && updateDto.name !== server.name) {
      const existingServer = await this.sshServerRepository.findByName(updateDto.name);
      if (existingServer && existingServer.id !== id) {
        throw new AppException(
          ErrorCode.CONFLICT,
          `SSH server với tên "${updateDto.name}" đã tồn tại`,
        );
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData: Partial<SshServer> = {
      ...updateDto,
      updatedBy,
    };

    // Mã hóa password mới nếu có
    if (updateDto.password) {
      updateData.passwordEncrypted = await this.encryptionService.encrypt(updateDto.password);
      delete updateData.password;
    }

    // Mã hóa private key mới nếu có
    if (updateDto.privateKey) {
      updateData.privateKeyEncrypted = await this.encryptionService.encrypt(updateDto.privateKey);
      delete updateData.privateKey;
    }

    // Mã hóa passphrase mới nếu có
    if (updateDto.passphrase) {
      updateData.passphraseEncrypted = await this.encryptionService.encrypt(updateDto.passphrase);
      delete updateData.passphrase;
    }

    const updatedServer = await this.sshServerRepository.update(id, updateData);
    
    this.logger.log(`Đã cập nhật SSH server: ${updatedServer.name} (ID: ${id})`);
    
    return updatedServer;
  }

  /**
   * Xóa mềm SSH server
   * @param id ID SSH server
   */
  async softDelete(id: number): Promise<void> {
    const server = await this.findByIdOrThrow(id);
    
    await this.sshServerRepository.softDelete(id);
    
    this.logger.log(`Đã xóa mềm SSH server: ${server.name} (ID: ${id})`);
  }

  /**
   * Xóa cứng SSH server
   * @param id ID SSH server
   */
  async hardDelete(id: number): Promise<void> {
    const server = await this.findByIdOrThrow(id);
    
    await this.sshServerRepository.hardDelete(id);
    
    this.logger.log(`Đã xóa cứng SSH server: ${server.name} (ID: ${id})`);
  }

  /**
   * Test kết nối SSH
   * @param id ID SSH server
   * @returns Kết quả test kết nối
   */
  async testConnection(id: number): Promise<{
    success: boolean;
    responseTime: number;
    message: string;
    error?: string;
    systemInfo?: any;
    testedAt: number;
  }> {
    const server = await this.findByIdOrThrow(id);
    
    if (!server.isActive) {
      return {
        success: false,
        responseTime: 0,
        message: 'SSH server đã bị vô hiệu hóa',
        error: 'Server is inactive',
        testedAt: Date.now(),
      };
    }

    const startTime = Date.now();

    try {
      // Tạo cấu hình kết nối
      const config = await this.sshConnectionService.createConnectionConfig(server);
      
      // Tạo kết nối
      const connectionId = await this.sshConnectionService.createConnection(config);
      
      try {
        // Thực hiện health check
        const healthCheck = await this.sshConnectionService.healthCheck(connectionId);
        
        const responseTime = Date.now() - startTime;
        
        // Cập nhật trạng thái kết nối thành công
        await this.sshServerRepository.updateConnectionStatus(id, 'connected');
        
        return {
          success: true,
          responseTime,
          message: 'Kết nối SSH thành công',
          systemInfo: healthCheck.systemInfo,
          testedAt: Date.now(),
        };
        
      } finally {
        // Đóng kết nối
        await this.sshConnectionService.closeConnection(connectionId);
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // Cập nhật trạng thái kết nối thất bại
      await this.sshServerRepository.updateConnectionStatus(id, 'error', error.message);
      
      this.logger.error(`Test kết nối SSH thất bại cho server ${server.name}: ${error.message}`);
      
      return {
        success: false,
        responseTime,
        message: 'Kết nối SSH thất bại',
        error: error.message,
        testedAt: Date.now(),
      };
    }
  }

  /**
   * Lấy thống kê SSH servers
   * @returns Thống kê SSH servers
   */
  async getStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    connected: number;
    disconnected: number;
  }> {
    return this.sshServerRepository.getStatistics();
  }

  /**
   * Kích hoạt SSH server
   * @param id ID SSH server
   * @param updatedBy ID người cập nhật
   */
  async activate(id: number, updatedBy: number): Promise<void> {
    await this.update(id, { isActive: true }, updatedBy);
    this.logger.log(`Đã kích hoạt SSH server ID: ${id}`);
  }

  /**
   * Vô hiệu hóa SSH server
   * @param id ID SSH server
   * @param updatedBy ID người cập nhật
   */
  async deactivate(id: number, updatedBy: number): Promise<void> {
    await this.update(id, { isActive: false }, updatedBy);
    this.logger.log(`Đã vô hiệu hóa SSH server ID: ${id}`);
  }

  /**
   * Kiểm tra quyền truy cập SSH server
   * @param id ID SSH server
   * @param userId ID người dùng
   * @returns True nếu có quyền truy cập
   */
  async checkAccess(id: number, userId: number): Promise<boolean> {
    const server = await this.findById(id);
    if (!server) {
      return false;
    }

    // TODO: Implement role-based access control
    // Hiện tại cho phép tất cả users trong cùng tenant
    return true;
  }

  /**
   * Lấy SSH server với thông tin đã giải mã (chỉ dùng nội bộ)
   * @param id ID SSH server
   * @returns SSH server với credentials đã giải mã
   */
  async getServerWithDecryptedCredentials(id: number): Promise<SshServer & {
    decryptedPassword?: string;
    decryptedPrivateKey?: string;
    decryptedPassphrase?: string;
  }> {
    const server = await this.findByIdOrThrow(id);
    
    const result: any = { ...server };

    // Giải mã password nếu có
    if (server.passwordEncrypted) {
      try {
        result.decryptedPassword = await this.encryptionService.decrypt(server.passwordEncrypted);
      } catch (error) {
        this.logger.error(`Lỗi giải mã password cho server ${server.name}: ${error.message}`);
      }
    }

    // Giải mã private key nếu có
    if (server.privateKeyEncrypted) {
      try {
        result.decryptedPrivateKey = await this.encryptionService.decrypt(server.privateKeyEncrypted);
      } catch (error) {
        this.logger.error(`Lỗi giải mã private key cho server ${server.name}: ${error.message}`);
      }
    }

    // Giải mã passphrase nếu có
    if (server.passphraseEncrypted) {
      try {
        result.decryptedPassphrase = await this.encryptionService.decrypt(server.passphraseEncrypted);
      } catch (error) {
        this.logger.error(`Lỗi giải mã passphrase cho server ${server.name}: ${error.message}`);
      }
    }

    return result;
  }
}
