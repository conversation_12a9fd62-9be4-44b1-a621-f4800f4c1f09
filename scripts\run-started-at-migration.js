const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * Script chạy migration thêm cột started_at vào bảng todos
 */

// Tạo client kết nối database
function createClient() {
  const config = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    user: process.env.DB_USERNAME || 'postgres', // Sử dụng 'user' thay vì 'username'
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'redai_db',
  };

  // Thêm SSL config nếu cần
  if (process.env.DB_SSL === 'true') {
    config.ssl = {
      rejectUnauthorized: false // Cho phép self-signed certificates
    };
  }

  console.log('🔗 Database connection config:');
  console.log(`   Host: ${config.host}`);
  console.log(`   Port: ${config.port}`);
  console.log(`   User: ${config.user}`);
  console.log(`   Database: ${config.database}`);
  console.log(`   SSL: ${config.ssl ? 'enabled' : 'disabled'}`);

  return new Client(config);
}

async function runMigration() {
  const client = createClient();

  try {
    console.log('\n🔄 Attempting to connect to database...');
    await client.connect();
    console.log('✅ Connected to database for started_at migration');

    // Test connection với simple query
    console.log('🧪 Testing database connection...');
    const testResult = await client.query('SELECT NOW() as current_time, version() as db_version');
    console.log('✅ Database connection test successful:');
    console.table(testResult.rows);

    // Đọc migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', '004-add-started-at-to-todos.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }
    
    const sqlContent = fs.readFileSync(migrationPath, 'utf8');

    console.log('\n🚀 Starting started_at migration...');
    console.log('📄 Running 004-add-started-at-to-todos.sql');

    // Chạy migration
    await client.query(sqlContent);

    console.log('✅ Migration completed successfully!');

    // Verification
    console.log('\n📊 Running verification checks...');
    
    // Kiểm tra cột started_at đã được tạo
    const columnCheck = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'todos' AND column_name = 'started_at'
    `);
    
    if (columnCheck.rows.length > 0) {
      console.log('✅ started_at column created successfully');
      console.table(columnCheck.rows);
    } else {
      console.log('❌ started_at column not found');
    }

    // Kiểm tra index
    const indexCheck = await client.query(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'todos' AND indexname = 'idx_todos_started_at'
    `);
    
    if (indexCheck.rows.length > 0) {
      console.log('✅ Index on started_at created successfully');
    } else {
      console.log('❌ Index on started_at not found');
    }

    // Thống kê todos
    const statsCheck = await client.query(`
      SELECT 
        COUNT(*) as total_todos,
        COUNT(started_at) as todos_with_started_at,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_todos,
        COUNT(CASE WHEN status = 'in_progress' AND started_at IS NOT NULL THEN 1 END) as in_progress_with_started_at
      FROM todos
    `);
    
    console.log('\n📈 Todos Statistics:');
    console.table(statsCheck.rows);

    // Kiểm tra migration history
    const migrationHistory = await client.query(`
      SELECT migration_name, status, started_at, completed_at, error_message
      FROM migration_history 
      WHERE migration_name = '004-add-started-at-to-todos'
      ORDER BY started_at DESC
    `);
    
    console.log('\n📋 Migration History:');
    console.table(migrationHistory.rows);

    await client.end();
    console.log('\n✅ started_at migration verification completed!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    
    try {
      await client.end();
    } catch (closeError) {
      console.error('Error closing connection:', closeError.message);
    }
    
    process.exit(1);
  }
}

// Chạy migration
console.log('🚀 STARTING STARTED_AT MIGRATION FOR TODOS TABLE');
console.log('================================================');

runMigration()
  .then(() => {
    console.log('\n🎯 MIGRATION SUMMARY:');
    console.log('- ✅ started_at column added to todos table');
    console.log('- ✅ Index created for performance optimization');
    console.log('- ✅ Existing IN_PROGRESS todos updated with started_at');
    console.log('- ✅ Database ready for tracking work start times');
    console.log('\n🔄 You can now use the startedAt field in your Todo APIs');
  })
  .catch((error) => {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  });
