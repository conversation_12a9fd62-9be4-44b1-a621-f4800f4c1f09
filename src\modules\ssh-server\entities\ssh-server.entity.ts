import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * Entity đại diện cho thông tin server SSH
 * Lưu trữ thông tin kết nối SSH với mã hóa credentials
 */
@Entity('ssh_servers')
@Index(['tenantId'])
@Index(['tenantId', 'name'], { unique: true })
@Index(['tenantId', 'isActive'])
@Index(['createdBy'])
export class SshServer {
  /**
   * ID duy nhất của server SSH
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID tenant để phân tách dữ liệu
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;

  /**
   * Tên server SSH (duy nhất trong tenant)
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Địa chỉ IP hoặc hostname của server
   */
  @Column({ name: 'host', type: 'varchar', length: 255, nullable: false })
  host: string;

  /**
   * Port SSH (mặc định 22)
   */
  @Column({ name: 'port', type: 'integer', default: 22 })
  port: number;

  /**
   * Username để đăng nhập SSH
   */
  @Column({ name: 'username', type: 'varchar', length: 255, nullable: false })
  username: string;

  /**
   * Password đã mã hóa (nullable nếu dùng private key)
   */
  @Column({ name: 'password_encrypted', type: 'text', nullable: true })
  passwordEncrypted: string | null;

  /**
   * Private key đã mã hóa (nullable nếu dùng password)
   */
  @Column({ name: 'private_key_encrypted', type: 'text', nullable: true })
  privateKeyEncrypted: string | null;

  /**
   * Passphrase cho private key (đã mã hóa)
   */
  @Column({ name: 'passphrase_encrypted', type: 'text', nullable: true })
  passphraseEncrypted: string | null;

  /**
   * Mô tả server
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Trạng thái hoạt động của server
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * Thời gian timeout kết nối (milliseconds)
   */
  @Column({ name: 'connection_timeout', type: 'integer', default: 30000 })
  connectionTimeout: number;

  /**
   * Thời gian timeout thực thi lệnh (milliseconds)
   */
  @Column({ name: 'command_timeout', type: 'integer', default: 300000 })
  commandTimeout: number;

  /**
   * Số lần thử lại khi kết nối thất bại
   */
  @Column({ name: 'retry_attempts', type: 'integer', default: 3 })
  retryAttempts: number;

  /**
   * Thời gian delay giữa các lần thử lại (milliseconds)
   */
  @Column({ name: 'retry_delay', type: 'integer', default: 5000 })
  retryDelay: number;

  /**
   * Thời gian kết nối thành công lần cuối
   */
  @Column({ name: 'last_connected_at', type: 'bigint', nullable: true })
  lastConnectedAt: number | null;

  /**
   * Trạng thái kết nối cuối cùng
   */
  @Column({ name: 'last_connection_status', type: 'varchar', length: 50, nullable: true })
  lastConnectionStatus: string | null;

  /**
   * Lỗi kết nối cuối cùng
   */
  @Column({ name: 'last_connection_error', type: 'text', nullable: true })
  lastConnectionError: string | null;

  /**
   * ID người tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: false })
  createdBy: number;

  /**
   * ID người cập nhật cuối
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Thời gian tạo (timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;
}
