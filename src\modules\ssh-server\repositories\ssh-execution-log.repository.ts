import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { SshExecutionLog } from '../entities/ssh-execution-log.entity';

/**
 * Repository cho entity SshExecutionLog với tenant isolation
 */
@Injectable()
export class SshExecutionLogRepository {
  private readonly logger = new Logger(SshExecutionLogRepository.name);

  constructor(
    @InjectRepository(SshExecutionLog)
    private readonly repository: Repository<SshExecutionLog>,
  ) {}

  /**
   * Tạo log thực thi mới
   * @param data Dữ liệu log thực thi
   * @returns Log thực thi đã tạo
   */
  async create(data: Partial<SshExecutionLog>): Promise<SshExecutionLog> {
    const now = Date.now();
    const log = this.repository.create({
      ...data,
      createdAt: now,
      executedAt: data.executedAt || now,
    });
    
    const result = await this.repository.save(log);
    this.logger.debug(`Đã tạo SSH execution log mới (ID: ${result.id})`);
    return result;
  }

  /**
   * Tìm log thực thi theo ID
   * @param id ID log thực thi
   * @returns Log thực thi hoặc null
   */
  async findById(id: number): Promise<SshExecutionLog | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm log thực thi theo job ID
   * @param jobId ID job queue
   * @returns Log thực thi hoặc null
   */
  async findByJobId(jobId: string): Promise<SshExecutionLog | null> {
    return this.repository.findOne({ where: { jobId } });
  }

  /**
   * Lấy danh sách log thực thi với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách log thực thi và tổng số
   */
  async findWithPagination(options: {
    page?: number;
    limit?: number;
    sshServerId?: number;
    status?: string;
    executionType?: string;
    executedBy?: number;
    fromDate?: number;
    toDate?: number;
    search?: string;
  }): Promise<{ items: SshExecutionLog[]; total: number }> {
    const { 
      page = 1, 
      limit = 10, 
      sshServerId, 
      status, 
      executionType,
      executedBy,
      fromDate,
      toDate,
      search 
    } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.repository.createQueryBuilder('log');

    // Lọc theo server SSH
    if (sshServerId) {
      queryBuilder.andWhere('log.sshServerId = :sshServerId', { sshServerId });
    }

    // Lọc theo trạng thái
    if (status) {
      queryBuilder.andWhere('log.status = :status', { status });
    }

    // Lọc theo loại thực thi
    if (executionType) {
      queryBuilder.andWhere('log.executionType = :executionType', { executionType });
    }

    // Lọc theo người thực thi
    if (executedBy) {
      queryBuilder.andWhere('log.executedBy = :executedBy', { executedBy });
    }

    // Lọc theo khoảng thời gian
    if (fromDate && toDate) {
      queryBuilder.andWhere('log.executedAt BETWEEN :fromDate AND :toDate', {
        fromDate,
        toDate,
      });
    } else if (fromDate) {
      queryBuilder.andWhere('log.executedAt >= :fromDate', { fromDate });
    } else if (toDate) {
      queryBuilder.andWhere('log.executedAt <= :toDate', { toDate });
    }

    // Tìm kiếm trong command hoặc output
    if (search) {
      queryBuilder.andWhere(
        '(log.command ILIKE :search OR log.output ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Sắp xếp theo thời gian thực thi mới nhất
    queryBuilder.orderBy('log.executedAt', 'DESC');

    // Phân trang
    queryBuilder.skip(skip).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return { items, total };
  }

  /**
   * Lấy log thực thi gần đây của server SSH
   * @param sshServerId ID server SSH
   * @param limit Số lượng log
   * @returns Danh sách log thực thi
   */
  async findRecentByServerId(sshServerId: number, limit: number = 10): Promise<SshExecutionLog[]> {
    return this.repository.find({
      where: { sshServerId },
      order: { executedAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * Cập nhật trạng thái log thực thi
   * @param id ID log thực thi
   * @param status Trạng thái mới
   * @param output Output của lệnh
   * @param errorOutput Error output của lệnh
   * @param exitCode Exit code của lệnh
   * @param executionTimeMs Thời gian thực thi
   */
  async updateStatus(
    id: number,
    status: SshExecutionLog['status'],
    output?: string,
    errorOutput?: string,
    exitCode?: number,
    executionTimeMs?: number,
  ): Promise<void> {
    const updateData: Partial<SshExecutionLog> = {
      status,
      completedAt: ['completed', 'failed', 'timeout', 'cancelled'].includes(status) 
        ? Date.now() 
        : undefined,
    };

    if (output !== undefined) updateData.output = output;
    if (errorOutput !== undefined) updateData.errorOutput = errorOutput;
    if (exitCode !== undefined) updateData.exitCode = exitCode;
    if (executionTimeMs !== undefined) updateData.executionTimeMs = executionTimeMs;

    await this.repository.update(id, updateData);
    this.logger.debug(`Cập nhật trạng thái SSH execution log ${id}: ${status}`);
  }

  /**
   * Lấy thống kê thực thi theo server SSH
   * @param sshServerId ID server SSH
   * @param fromDate Từ ngày (timestamp)
   * @param toDate Đến ngày (timestamp)
   * @returns Thống kê thực thi
   */
  async getStatisticsByServerId(
    sshServerId: number,
    fromDate?: number,
    toDate?: number,
  ): Promise<{
    total: number;
    completed: number;
    failed: number;
    running: number;
    avgExecutionTime: number;
  }> {
    const queryBuilder = this.repository.createQueryBuilder('log')
      .where('log.sshServerId = :sshServerId', { sshServerId });

    if (fromDate && toDate) {
      queryBuilder.andWhere('log.executedAt BETWEEN :fromDate AND :toDate', {
        fromDate,
        toDate,
      });
    }

    const [total, completed, failed, running, avgResult] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('log.status = :status', { status: 'completed' }).getCount(),
      queryBuilder.clone().andWhere('log.status = :status', { status: 'failed' }).getCount(),
      queryBuilder.clone().andWhere('log.status = :status', { status: 'running' }).getCount(),
      queryBuilder.clone()
        .select('AVG(log.executionTimeMs)', 'avg')
        .andWhere('log.executionTimeMs IS NOT NULL')
        .getRawOne(),
    ]);

    return {
      total,
      completed,
      failed,
      running,
      avgExecutionTime: Math.round(avgResult?.avg || 0),
    };
  }

  /**
   * Xóa log thực thi cũ
   * @param olderThanDays Xóa log cũ hơn số ngày
   * @returns Số lượng log đã xóa
   */
  async deleteOldLogs(olderThanDays: number): Promise<number> {
    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    const result = await this.repository.delete({
      executedAt: Between(0, cutoffDate),
    });

    const deletedCount = result.affected || 0;
    this.logger.log(`Đã xóa ${deletedCount} SSH execution logs cũ hơn ${olderThanDays} ngày`);
    
    return deletedCount;
  }

  /**
   * Lấy log thực thi đang chạy
   * @returns Danh sách log thực thi đang chạy
   */
  async findRunningExecutions(): Promise<SshExecutionLog[]> {
    return this.repository.find({
      where: { status: 'running' },
      order: { executedAt: 'ASC' },
    });
  }
}
