import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt, Min, IsOptional, MaxLength } from 'class-validator';

/**
 * DTO cho yêu cầu lấy URL upload ảnh bình luận
 */
export class CommentImageUploadUrlDto {
  /**
   * ID của công việc mà bình luận thuộc về
   * @example 123
   */
  @ApiProperty({
    description: 'ID của công việc mà bình luận thuộc về',
    example: 123,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * Tên file ảnh
   * @example "comment-image.jpg"
   */
  @ApiProperty({
    description: 'Tên file ảnh',
    example: 'comment-image.jpg',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString({ message: 'Tên file phải là chuỗi' })
  @MaxLength(255, { message: 'Tên file không được vượt quá 255 ký tự' })
  fileName: string;

  /**
   * Loại MIME của file ảnh
   * @example "image/jpeg"
   */
  @ApiProperty({
    description: 'Loại MIME của file ảnh',
    example: 'image/jpeg',
    required: true,
  })
  @IsNotEmpty({ message: 'Loại MIME không được để trống' })
  @IsString({ message: 'Loại MIME phải là chuỗi' })
  @MaxLength(100, { message: 'Loại MIME không được vượt quá 100 ký tự' })
  mimeType: string;

  /**
   * Kích thước file (bytes)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1024000,
    required: true,
  })
  @IsNotEmpty({ message: 'Kích thước file không được để trống' })
  @IsInt({ message: 'Kích thước file phải là số nguyên' })
  @Min(1, { message: 'Kích thước file phải lớn hơn 0' })
  fileSize: number;

  /**
   * Mô tả ảnh (tùy chọn)
   * @example "Ảnh minh họa cho bình luận"
   */
  @ApiProperty({
    description: 'Mô tả ảnh (tùy chọn)',
    example: 'Ảnh minh họa cho bình luận',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  @MaxLength(500, { message: 'Mô tả không được vượt quá 500 ký tự' })
  description?: string;
}

/**
 * DTO cho response URL upload ảnh bình luận
 */
export class CommentImageUploadUrlResponseDto {
  /**
   * URL tạm thời để upload ảnh
   * @example "https://storage.googleapis.com/bucket/upload-url"
   */
  @ApiProperty({
    description: 'URL tạm thời để upload ảnh',
    example: 'https://storage.googleapis.com/bucket/upload-url',
  })
  uploadUrl: string;

  /**
   * URL công khai của ảnh sau khi upload thành công
   * @example "https://storage.googleapis.com/bucket/todo-comments/123/image.jpg"
   */
  @ApiProperty({
    description: 'URL công khai của ảnh sau khi upload thành công',
    example: 'https://storage.googleapis.com/bucket/todo-comments/123/image.jpg',
  })
  publicUrl: string;

  /**
   * Thời gian hết hạn của URL upload (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian hết hạn của URL upload (timestamp)',
    example: 1625097600000,
  })
  expiresAt: number;

  /**
   * ID tạm thời để tracking upload
   * @example "upload_123_456_789"
   */
  @ApiProperty({
    description: 'ID tạm thời để tracking upload',
    example: 'upload_123_456_789',
  })
  uploadId: string;

  /**
   * Metadata bổ sung
   */
  @ApiProperty({
    description: 'Metadata bổ sung',
    example: {
      maxFileSize: 10485760,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    },
  })
  metadata: {
    maxFileSize: number;
    allowedMimeTypes: string[];
  };
}
