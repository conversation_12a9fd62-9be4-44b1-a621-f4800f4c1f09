/**
 * Constants cho SSH commands và deployment templates
 */

/**
 * <PERSON><PERSON><PERSON> lệnh SSH phổ biến
 */
export const SSH_COMMANDS = {
  // System commands
  SYSTEM: {
    UPTIME: 'uptime',
    FREE_MEMORY: 'free -h',
    DISK_USAGE: 'df -h',
    LOAD_AVERAGE: 'cat /proc/loadavg',
    WHOAMI: 'whoami',
    PWD: 'pwd',
    LS: 'ls -la',
    PS: 'ps aux',
    TOP: 'top -bn1',
    NETSTAT: 'netstat -tulpn',
  },

  // Git commands
  GIT: {
    STATUS: 'git status',
    PULL: 'git pull',
    FETCH: 'git fetch',
    CHECKOUT: (branch: string) => `git checkout ${branch}`,
    BRANCH: 'git branch -a',
    LOG: (count: number = 5) => `git log --oneline -${count}`,
    RESET_HARD: (commit: string) => `git reset --hard ${commit}`,
    CLONE: (repo: string, dir?: string) => `git clone ${repo}${dir ? ` ${dir}` : ''}`,
    REMOTE: 'git remote -v',
    DIFF: 'git diff',
    STASH: 'git stash',
    STASH_POP: 'git stash pop',
  },

  // Node.js commands
  NODE: {
    VERSION: 'node --version',
    NPM_VERSION: 'npm --version',
    NPM_INSTALL: 'npm install',
    NPM_CI: 'npm ci',
    NPM_BUILD: 'npm run build',
    NPM_START: 'npm start',
    NPM_STOP: 'npm stop',
    NPM_RESTART: 'npm restart',
    NPM_TEST: 'npm test',
    YARN_VERSION: 'yarn --version',
    YARN_INSTALL: 'yarn install',
    YARN_BUILD: 'yarn build',
    YARN_START: 'yarn start',
  },

  // Docker commands
  DOCKER: {
    VERSION: 'docker --version',
    PS: 'docker ps',
    PS_ALL: 'docker ps -a',
    IMAGES: 'docker images',
    BUILD: (tag: string, path: string = '.') => `docker build -t ${tag} ${path}`,
    RUN: (image: string, options?: string) => `docker run ${options || ''} ${image}`,
    STOP: (container: string) => `docker stop ${container}`,
    START: (container: string) => `docker start ${container}`,
    RESTART: (container: string) => `docker restart ${container}`,
    REMOVE: (container: string) => `docker rm ${container}`,
    LOGS: (container: string) => `docker logs ${container}`,
    EXEC: (container: string, command: string) => `docker exec ${container} ${command}`,
    COMPOSE_UP: 'docker-compose up -d',
    COMPOSE_DOWN: 'docker-compose down',
    COMPOSE_RESTART: 'docker-compose restart',
    COMPOSE_LOGS: 'docker-compose logs',
  },

  // PM2 commands
  PM2: {
    LIST: 'pm2 list',
    START: (app: string) => `pm2 start ${app}`,
    STOP: (app: string) => `pm2 stop ${app}`,
    RESTART: (app: string) => `pm2 restart ${app}`,
    DELETE: (app: string) => `pm2 delete ${app}`,
    RELOAD: (app: string) => `pm2 reload ${app}`,
    LOGS: (app?: string) => `pm2 logs${app ? ` ${app}` : ''}`,
    MONIT: 'pm2 monit',
    STATUS: 'pm2 status',
    SAVE: 'pm2 save',
    RESURRECT: 'pm2 resurrect',
  },

  // Service management (systemd)
  SERVICE: {
    STATUS: (service: string) => `systemctl status ${service}`,
    START: (service: string) => `sudo systemctl start ${service}`,
    STOP: (service: string) => `sudo systemctl stop ${service}`,
    RESTART: (service: string) => `sudo systemctl restart ${service}`,
    ENABLE: (service: string) => `sudo systemctl enable ${service}`,
    DISABLE: (service: string) => `sudo systemctl disable ${service}`,
    RELOAD: (service: string) => `sudo systemctl reload ${service}`,
    LIST: 'systemctl list-units --type=service',
  },

  // File operations
  FILE: {
    CAT: (file: string) => `cat ${file}`,
    TAIL: (file: string, lines: number = 100) => `tail -n ${lines} ${file}`,
    HEAD: (file: string, lines: number = 10) => `head -n ${lines} ${file}`,
    GREP: (pattern: string, file: string) => `grep "${pattern}" ${file}`,
    FIND: (path: string, name: string) => `find ${path} -name "${name}"`,
    CHMOD: (permissions: string, file: string) => `chmod ${permissions} ${file}`,
    CHOWN: (owner: string, file: string) => `chown ${owner} ${file}`,
    CP: (source: string, dest: string) => `cp ${source} ${dest}`,
    MV: (source: string, dest: string) => `mv ${source} ${dest}`,
    RM: (file: string) => `rm ${file}`,
    MKDIR: (dir: string) => `mkdir -p ${dir}`,
    RMDIR: (dir: string) => `rmdir ${dir}`,
  },
} as const;

/**
 * Templates cho deployment workflows
 */
export const DEPLOYMENT_TEMPLATES = {
  // Node.js deployment
  NODEJS: {
    name: 'Node.js Deployment',
    description: 'Standard Node.js application deployment',
    commands: [
      { command: 'git pull', description: 'Pull latest code' },
      { command: 'npm ci', description: 'Install dependencies' },
      { command: 'npm run build', description: 'Build application' },
      { command: 'pm2 restart app', description: 'Restart application' },
    ],
  },

  // React deployment
  REACT: {
    name: 'React Deployment',
    description: 'React application deployment with build',
    commands: [
      { command: 'git pull', description: 'Pull latest code' },
      { command: 'npm ci', description: 'Install dependencies' },
      { command: 'npm run build', description: 'Build React app' },
      { command: 'sudo systemctl restart nginx', description: 'Restart web server' },
    ],
  },

  // Docker deployment
  DOCKER: {
    name: 'Docker Deployment',
    description: 'Docker container deployment',
    commands: [
      { command: 'git pull', description: 'Pull latest code' },
      { command: 'docker-compose down', description: 'Stop containers' },
      { command: 'docker-compose build', description: 'Build new images' },
      { command: 'docker-compose up -d', description: 'Start containers' },
    ],
  },

  // NestJS deployment
  NESTJS: {
    name: 'NestJS Deployment',
    description: 'NestJS application deployment',
    commands: [
      { command: 'git pull', description: 'Pull latest code' },
      { command: 'npm ci', description: 'Install dependencies' },
      { command: 'npm run build', description: 'Build NestJS app' },
      { command: 'pm2 restart nestjs-app', description: 'Restart NestJS app' },
    ],
  },

  // Custom deployment
  CUSTOM: {
    name: 'Custom Deployment',
    description: 'Custom deployment workflow',
    commands: [],
  },
} as const;

/**
 * Health check commands
 */
export const HEALTH_CHECK_COMMANDS = {
  BASIC: [
    { command: 'uptime', description: 'System uptime' },
    { command: 'free -h', description: 'Memory usage' },
    { command: 'df -h /', description: 'Disk usage' },
  ],
  
  DETAILED: [
    { command: 'uptime', description: 'System uptime' },
    { command: 'free -h', description: 'Memory usage' },
    { command: 'df -h', description: 'Disk usage' },
    { command: 'cat /proc/loadavg', description: 'Load average' },
    { command: 'ps aux | head -10', description: 'Top processes' },
    { command: 'netstat -tulpn | grep LISTEN', description: 'Listening ports' },
  ],

  SERVICE_CHECK: (service: string) => [
    { command: `systemctl is-active ${service}`, description: `Check ${service} status` },
    { command: `systemctl is-enabled ${service}`, description: `Check ${service} enabled` },
  ],

  APPLICATION_CHECK: (port: number) => [
    { command: `curl -f http://localhost:${port}/health || echo "Health check failed"`, description: 'Application health check' },
    { command: `netstat -tulpn | grep :${port}`, description: `Check port ${port}` },
  ],
} as const;

/**
 * SSH connection defaults
 */
export const SSH_DEFAULTS = {
  PORT: 22,
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  COMMAND_TIMEOUT: 300000,   // 5 minutes
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 5000,         // 5 seconds
  MAX_CONNECTIONS: 10,
  IDLE_TIMEOUT: 600000,      // 10 minutes
} as const;

/**
 * SSH error codes
 */
export const SSH_ERROR_CODES = {
  CONNECTION_FAILED: 'SSH_CONNECTION_FAILED',
  AUTHENTICATION_FAILED: 'SSH_AUTHENTICATION_FAILED',
  COMMAND_TIMEOUT: 'SSH_COMMAND_TIMEOUT',
  COMMAND_FAILED: 'SSH_COMMAND_FAILED',
  INVALID_CONFIG: 'SSH_INVALID_CONFIG',
  CONNECTION_LOST: 'SSH_CONNECTION_LOST',
  MAX_RETRIES_EXCEEDED: 'SSH_MAX_RETRIES_EXCEEDED',
  PERMISSION_DENIED: 'SSH_PERMISSION_DENIED',
} as const;
