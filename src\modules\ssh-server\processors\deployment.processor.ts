import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, DeploymentJobName } from '@/shared/queue/queue.constants';
import { DeploymentService } from '../services/deployment.service';
import { SshCommandService } from '../services/ssh-command.service';
import { DeploymentHistoryRepository } from '../repositories/deployment-history.repository';

/**
 * Processor xử lý deployment jobs
 */
@Processor(QueueName.DEPLOYMENT)
export class DeploymentProcessor extends WorkerHost {
  private readonly logger = new Logger(DeploymentProcessor.name);

  constructor(
    private readonly deploymentService: DeploymentService,
    private readonly sshCommandService: SshCommandService,
    private readonly deploymentHistoryRepository: DeploymentHistoryRepository,
  ) {
    super();
  }

  /**
   * <PERSON><PERSON> lý job deploy project
   */
  @Processor(DeploymentJobName.DEPLOY_PROJECT)
  async handleDeployProject(job: Job<any>) {
    const { sshServerId, deploymentConfig, deployedBy } = job.data;
    
    this.logger.log(`Bắt đầu xử lý deployment job: ${job.id} - Project: ${deploymentConfig.projectName}`);

    try {
      await job.updateProgress(5);

      const result = await this.deploymentService.deploy(
        sshServerId,
        deploymentConfig,
        deployedBy,
        job.id,
      );

      // Cập nhật progress dựa trên trạng thái deployment
      if (result.status === 'running') {
        await job.updateProgress(50);
      }

      if (result.status === 'completed') {
        await job.updateProgress(100);
      } else if (result.status === 'failed') {
        await job.updateProgress(100);
      }

      this.logger.log(`Hoàn thành deployment job: ${job.id} - Status: ${result.status}`);

      return {
        success: result.status === 'completed',
        deploymentId: result.deploymentId,
        status: result.status,
        result,
      };
    } catch (error) {
      this.logger.error(`Lỗi xử lý deployment job ${job.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job rollback deployment
   */
  @Processor(DeploymentJobName.ROLLBACK_DEPLOYMENT)
  async handleRollbackDeployment(job: Job<any>) {
    const { sshServerId, deploymentId, deployedBy } = job.data;
    
    this.logger.log(`Bắt đầu xử lý rollback job: ${job.id} - Deployment: ${deploymentId}`);

    try {
      await job.updateProgress(10);

      const result = await this.deploymentService.rollback(
        sshServerId,
        deploymentId,
        deployedBy,
        job.id,
      );

      // Cập nhật progress
      if (result.status === 'running') {
        await job.updateProgress(50);
      }

      if (result.status === 'completed') {
        await job.updateProgress(100);
      } else if (result.status === 'failed') {
        await job.updateProgress(100);
      }

      this.logger.log(`Hoàn thành rollback job: ${job.id} - Status: ${result.status}`);

      return {
        success: result.status === 'completed',
        deploymentId: result.deploymentId,
        originalDeploymentId: deploymentId,
        status: result.status,
        result,
      };
    } catch (error) {
      this.logger.error(`Lỗi xử lý rollback job ${job.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job deploy với template
   */
  @Processor(DeploymentJobName.DEPLOY_WITH_TEMPLATE)
  async handleDeployWithTemplate(job: Job<any>) {
    const { sshServerId, templateName, projectName, customCommands, executedBy, metadata } = job.data;
    
    this.logger.log(`Bắt đầu xử lý deployment template job: ${job.id} - Template: ${templateName}`);

    try {
      await job.updateProgress(10);

      const { result, logIds } = await this.sshCommandService.executeDeploymentTemplate(
        sshServerId,
        templateName,
        executedBy,
        customCommands,
        {
          jobId: job.id,
          metadata: {
            ...metadata,
            projectName,
            source: 'queue',
          },
        },
      );

      // Cập nhật progress dựa trên số lệnh đã hoàn thành
      const progressStep = 80 / result.totalCommands;
      for (let i = 0; i < result.results.length; i++) {
        await job.updateProgress(10 + (i + 1) * progressStep);
      }

      await job.updateProgress(100);

      this.logger.log(`Hoàn thành deployment template job: ${job.id} - ${result.successCount}/${result.totalCommands} thành công`);

      return {
        success: result.overallSuccess,
        templateName,
        projectName,
        logIds,
        result,
      };
    } catch (error) {
      this.logger.error(`Lỗi xử lý deployment template job ${job.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Event handler khi job hoàn thành
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(`Deployment job ${job.id} hoàn thành: ${JSON.stringify(result)}`);
  }

  /**
   * Event handler khi job thất bại
   */
  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`Deployment job ${job.id} thất bại: ${error.message}`, error.stack);
  }

  /**
   * Event handler khi job bị stalled
   */
  @OnWorkerEvent('stalled')
  onStalled(job: Job) {
    this.logger.warn(`Deployment job ${job.id} bị stalled`);
  }

  /**
   * Event handler khi job progress được cập nhật
   */
  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: number) {
    this.logger.debug(`Deployment job ${job.id} progress: ${progress}%`);
  }

  /**
   * Event handler khi job active
   */
  @OnWorkerEvent('active')
  onActive(job: Job) {
    this.logger.log(`Deployment job ${job.id} đã bắt đầu xử lý`);
  }

  /**
   * Event handler khi job waiting
   */
  @OnWorkerEvent('waiting')
  onWaiting(job: Job) {
    this.logger.debug(`Deployment job ${job.id} đang chờ xử lý`);
  }
}
