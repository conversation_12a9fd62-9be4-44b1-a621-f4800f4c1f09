import { Injectable, Logger } from '@nestjs/common';
import { SshCommandService } from '../services/ssh-command.service';
import { SshExecutionLogRepository } from '../repositories/ssh-execution-log.repository';
import { SSH_COMMANDS } from '../constants/ssh-commands.constants';

/**
 * Tool để thực thi lệnh SSH thông qua chat interface
 */
@Injectable()
export class SshCommandExecutionTool {
  private readonly logger = new Logger(SshCommandExecutionTool.name);

  constructor(
    private readonly sshCommandService: SshCommandService,
    private readonly sshExecutionLogRepository: SshExecutionLogRepository,
  ) {}

  /**
   * Thực thi lệnh SSH đơn lẻ
   */
  async executeCommand(params: {
    sshServerId: number;
    command: string;
    workingDirectory?: string;
    environment?: Record<string, string>;
    timeout?: number;
    description?: string;
  }, executedBy: number) {
    try {
      const { result, logId } = await this.sshCommandService.executeCommand(
        params.sshServerId,
        params.command,
        executedBy,
        {
          workingDirectory: params.workingDirectory,
          environment: params.environment,
          timeout: params.timeout,
          executionType: 'manual',
          metadata: {
            description: params.description,
            source: 'chat',
          },
        },
      );

      return {
        success: result.success,
        message: result.success 
          ? `Lệnh thực thi thành công trong ${result.executionTime}ms`
          : `Lệnh thực thi thất bại: ${result.error}`,
        data: {
          logId,
          command: result.command,
          stdout: result.stdout,
          stderr: result.stderr,
          exitCode: result.exitCode,
          executionTime: result.executionTime,
          success: result.success,
          error: result.error,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi thực thi lệnh SSH: ${error.message}`);
      return {
        success: false,
        message: `Lỗi thực thi lệnh SSH: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Thực thi batch commands
   */
  async executeBatchCommands(params: {
    sshServerId: number;
    commands: Array<{
      command: string;
      description?: string;
      workingDirectory?: string;
      environment?: Record<string, string>;
      continueOnError?: boolean;
    }>;
    description?: string;
  }, executedBy: number) {
    try {
      const { result, logIds } = await this.sshCommandService.executeBatchCommands(
        params.sshServerId,
        params.commands,
        executedBy,
        {
          executionType: 'batch',
          metadata: {
            description: params.description,
            source: 'chat',
          },
        },
      );

      return {
        success: result.overallSuccess,
        message: result.overallSuccess 
          ? `Batch commands thực thi thành công (${result.successCount}/${result.totalCommands})`
          : `Batch commands hoàn thành với ${result.failureCount} lỗi`,
        data: {
          logIds,
          totalCommands: result.totalCommands,
          successCount: result.successCount,
          failureCount: result.failureCount,
          totalExecutionTime: result.totalExecutionTime,
          overallSuccess: result.overallSuccess,
          results: result.results.map(cmdResult => ({
            command: cmdResult.command,
            description: cmdResult.description,
            stdout: cmdResult.stdout,
            stderr: cmdResult.stderr,
            exitCode: cmdResult.exitCode,
            executionTime: cmdResult.executionTime,
            success: cmdResult.success,
            error: cmdResult.error,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi thực thi batch commands: ${error.message}`);
      return {
        success: false,
        message: `Lỗi thực thi batch commands: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Thực thi deployment template
   */
  async executeDeploymentTemplate(params: {
    sshServerId: number;
    templateName: 'NODEJS' | 'REACT' | 'DOCKER' | 'NESTJS' | 'CUSTOM';
    projectName: string;
    customCommands?: Array<{
      command: string;
      description?: string;
      workingDirectory?: string;
      environment?: Record<string, string>;
      continueOnError?: boolean;
    }>;
  }, executedBy: number) {
    try {
      const { result, logIds } = await this.sshCommandService.executeDeploymentTemplate(
        params.sshServerId,
        params.templateName,
        executedBy,
        params.customCommands,
        {
          metadata: {
            projectName: params.projectName,
            source: 'chat',
          },
        },
      );

      return {
        success: result.overallSuccess,
        message: result.overallSuccess 
          ? `Deployment template "${params.templateName}" thực thi thành công`
          : `Deployment template "${params.templateName}" thất bại`,
        data: {
          logIds,
          templateName: params.templateName,
          projectName: params.projectName,
          totalCommands: result.totalCommands,
          successCount: result.successCount,
          failureCount: result.failureCount,
          totalExecutionTime: result.totalExecutionTime,
          overallSuccess: result.overallSuccess,
          results: result.results.map(cmdResult => ({
            command: cmdResult.command,
            description: cmdResult.description,
            stdout: cmdResult.stdout,
            stderr: cmdResult.stderr,
            exitCode: cmdResult.exitCode,
            executionTime: cmdResult.executionTime,
            success: cmdResult.success,
            error: cmdResult.error,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi thực thi deployment template: ${error.message}`);
      return {
        success: false,
        message: `Lỗi thực thi deployment template: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy danh sách execution logs
   */
  async getExecutionLogs(params: {
    page?: number;
    limit?: number;
    sshServerId?: number;
    status?: string;
    executionType?: string;
    search?: string;
  }, executedBy?: number) {
    try {
      const { items, total } = await this.sshExecutionLogRepository.findWithPagination({
        page: params.page || 1,
        limit: params.limit || 10,
        sshServerId: params.sshServerId,
        status: params.status,
        executionType: params.executionType,
        search: params.search,
        executedBy,
      });

      return {
        success: true,
        message: `Tìm thấy ${total} execution logs`,
        data: {
          logs: items.map(log => ({
            id: log.id,
            sshServerId: log.sshServerId,
            command: log.command,
            workingDirectory: log.workingDirectory,
            output: log.output,
            errorOutput: log.errorOutput,
            exitCode: log.exitCode,
            executionTimeMs: log.executionTimeMs,
            status: log.status,
            executionType: log.executionType,
            metadata: log.metadata ? JSON.parse(log.metadata) : null,
            executedBy: log.executedBy,
            executedAt: log.executedAt,
            completedAt: log.completedAt,
          })),
          total,
          page: params.page || 1,
          limit: params.limit || 10,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy execution logs: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy execution logs: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy execution log theo ID
   */
  async getExecutionLogById(logId: number) {
    try {
      const log = await this.sshExecutionLogRepository.findById(logId);

      if (!log) {
        return {
          success: false,
          message: `Execution log với ID ${logId} không tồn tại`,
          data: null,
        };
      }

      return {
        success: true,
        message: `Thông tin execution log ${logId}`,
        data: {
          id: log.id,
          sshServerId: log.sshServerId,
          command: log.command,
          workingDirectory: log.workingDirectory,
          output: log.output,
          errorOutput: log.errorOutput,
          exitCode: log.exitCode,
          executionTimeMs: log.executionTimeMs,
          status: log.status,
          executionType: log.executionType,
          metadata: log.metadata ? JSON.parse(log.metadata) : null,
          executedBy: log.executedBy,
          executedAt: log.executedAt,
          completedAt: log.completedAt,
          createdAt: log.createdAt,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy execution log: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy execution log: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy danh sách lệnh phổ biến
   */
  async getCommonCommands(category?: keyof typeof SSH_COMMANDS) {
    try {
      if (category) {
        const commands = this.sshCommandService.getCommonCommands(category);
        return {
          success: true,
          message: `Danh sách lệnh ${category}`,
          data: {
            category,
            commands: Object.entries(commands).map(([key, command]) => ({
              key,
              command: typeof command === 'function' ? 'Function' : command,
              description: `${category} command: ${key}`,
            })),
          },
        };
      } else {
        const allCategories = Object.keys(SSH_COMMANDS);
        return {
          success: true,
          message: 'Danh sách tất cả categories lệnh SSH',
          data: {
            categories: allCategories.map(cat => ({
              category: cat,
              description: `${cat} commands`,
              commandCount: Object.keys(SSH_COMMANDS[cat as keyof typeof SSH_COMMANDS]).length,
            })),
          },
        };
      }
    } catch (error) {
      this.logger.error(`Lỗi lấy danh sách lệnh: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy danh sách lệnh: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy danh sách deployment templates
   */
  async getDeploymentTemplates() {
    try {
      const templates = this.sshCommandService.getDeploymentTemplates();

      return {
        success: true,
        message: 'Danh sách deployment templates',
        data: {
          templates: Object.entries(templates).map(([key, template]) => ({
            key,
            name: template.name,
            description: template.description,
            commandCount: template.commands.length,
            commands: template.commands,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy deployment templates: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy deployment templates: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy thống kê execution theo server
   */
  async getExecutionStatistics(sshServerId: number, fromDate?: number, toDate?: number) {
    try {
      const statistics = await this.sshExecutionLogRepository.getStatisticsByServerId(
        sshServerId,
        fromDate,
        toDate,
      );

      return {
        success: true,
        message: `Thống kê execution cho SSH server ${sshServerId}`,
        data: statistics,
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy thống kê execution: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy thống kê execution: ${error.message}`,
        data: null,
      };
    }
  }
}
