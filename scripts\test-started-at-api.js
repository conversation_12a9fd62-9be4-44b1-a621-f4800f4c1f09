const axios = require('axios');
require('dotenv').config();

/**
 * Script test API cập nhật startedAt
 */

const API_BASE_URL = 'http://localhost:3001/api';

// Mock JWT token - trong thực tế cần login để lấy token
const MOCK_JWT_TOKEN = 'your-jwt-token-here';

async function testStartedAtAPI() {
  console.log('🧪 TESTING STARTED_AT API');
  console.log('=========================');

  try {
    // Test 1: Tạo todo mới để test
    console.log('\n📋 Test 1: Creating test todo...');
    
    const createTodoResponse = await axios.post(
      `${API_BASE_URL}/todos`,
      {
        title: 'Test Todo for StartedAt API',
        description: 'Testing startedAt API functionality',
        priority: 'medium',
        expectedStars: 3
      },
      {
        headers: {
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const todoId = createTodoResponse.data.data.id;
    console.log(`✅ Todo created with ID: ${todoId}`);
    console.log(`   Title: ${createTodoResponse.data.data.title}`);
    console.log(`   StartedAt: ${createTodoResponse.data.data.startedAt}`);

    // Test 2: Cập nhật startedAt với thời gian cụ thể
    console.log('\n📋 Test 2: Updating startedAt with specific time...');
    
    const specificTime = Date.now() - (2 * 60 * 60 * 1000); // 2 giờ trước
    const updateResponse = await axios.patch(
      `${API_BASE_URL}/todos/${todoId}/started-at`,
      {
        startedAt: specificTime
      },
      {
        headers: {
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ StartedAt updated with specific time:');
    console.log(`   StartedAt: ${updateResponse.data.data.startedAt}`);
    console.log(`   Readable: ${new Date(updateResponse.data.data.startedAt).toLocaleString('vi-VN')}`);

    // Test 3: Cập nhật startedAt với thời gian hiện tại (không truyền startedAt)
    console.log('\n📋 Test 3: Updating startedAt with current time...');
    
    const updateCurrentResponse = await axios.patch(
      `${API_BASE_URL}/todos/${todoId}/started-at`,
      {}, // Không truyền startedAt
      {
        headers: {
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ StartedAt updated with current time:');
    console.log(`   StartedAt: ${updateCurrentResponse.data.data.startedAt}`);
    console.log(`   Readable: ${new Date(updateCurrentResponse.data.data.startedAt).toLocaleString('vi-VN')}`);

    // Test 4: Kiểm tra todo detail
    console.log('\n📋 Test 4: Checking todo details...');
    
    const detailResponse = await axios.get(
      `${API_BASE_URL}/todos/${todoId}`,
      {
        headers: {
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`
        }
      }
    );

    const todo = detailResponse.data.data;
    console.log('✅ Todo details:');
    console.log(`   ID: ${todo.id}`);
    console.log(`   Title: ${todo.title}`);
    console.log(`   Status: ${todo.status}`);
    console.log(`   StartedAt: ${todo.startedAt}`);
    console.log(`   CreatedAt: ${todo.createdAt}`);
    console.log(`   UpdatedAt: ${todo.updatedAt}`);

    // Test 5: Cleanup - xóa todo test
    console.log('\n🧹 Test 5: Cleaning up test todo...');
    
    await axios.delete(
      `${API_BASE_URL}/todos/${todoId}`,
      {
        headers: {
          'Authorization': `Bearer ${MOCK_JWT_TOKEN}`
        }
      }
    );

    console.log('✅ Test todo deleted successfully');

    console.log('\n🎯 ALL TESTS PASSED!');
    console.log('- ✅ Todo creation with startedAt field');
    console.log('- ✅ Update startedAt with specific timestamp');
    console.log('- ✅ Update startedAt with current time (auto)');
    console.log('- ✅ Todo detail includes startedAt field');
    console.log('- ✅ Cleanup completed');

  } catch (error) {
    console.error('\n❌ TEST FAILED:');
    
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Message: ${error.response.data?.message || error.response.statusText}`);
      console.error(`   Data:`, error.response.data);
    } else if (error.request) {
      console.error('   No response received from server');
      console.error('   Make sure the server is running on http://localhost:3001');
    } else {
      console.error(`   Error: ${error.message}`);
    }
  }
}

// Hướng dẫn sử dụng
console.log('📖 HƯỚNG DẪN TEST API STARTED_AT');
console.log('================================');
console.log('1. Đảm bảo server đang chạy: npm run start:dev');
console.log('2. Cần có JWT token hợp lệ để test');
console.log('3. Cập nhật MOCK_JWT_TOKEN trong script này');
console.log('4. Chạy: node scripts/test-started-at-api.js');
console.log('');
console.log('🔗 API Endpoints được test:');
console.log('- POST /api/todos (tạo todo)');
console.log('- PATCH /api/todos/:id/started-at (cập nhật startedAt)');
console.log('- GET /api/todos/:id (xem chi tiết)');
console.log('- DELETE /api/todos/:id (xóa todo)');
console.log('');

if (MOCK_JWT_TOKEN === 'your-jwt-token-here') {
  console.log('⚠️  CẢNH BÁO: Cần cập nhật JWT token để test API');
  console.log('   Lấy token từ login API hoặc Swagger UI');
} else {
  // Chạy test nếu có token
  testStartedAtAPI();
}
