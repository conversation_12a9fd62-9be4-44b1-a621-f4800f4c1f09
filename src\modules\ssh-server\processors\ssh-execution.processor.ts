import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, SshExecutionJobName } from '@/shared/queue/queue.constants';
import { SshCommandService } from '../services/ssh-command.service';
import { SshExecutionLogRepository } from '../repositories/ssh-execution-log.repository';

/**
 * Processor xử lý SSH execution jobs
 */
@Processor(QueueName.SSH_EXECUTION)
export class SshExecutionProcessor extends WorkerHost {
  private readonly logger = new Logger(SshExecutionProcessor.name);

  constructor(
    private readonly sshCommandService: SshCommandService,
    private readonly sshExecutionLogRepository: SshExecutionLogRepository,
  ) {
    super();
  }

  /**
   * Xử lý job thực thi lệnh SSH đơn lẻ
   */
  @Processor(SshExecutionJobName.EXECUTE_COMMAND)
  async handleExecuteCommand(job: Job<any>) {
    const { sshServerId, command, executedBy, options } = job.data;
    
    this.logger.log(`Bắt đầu xử lý SSH command job: ${job.id}`);

    try {
      // Cập nhật progress
      await job.updateProgress(10);

      const { result, logId } = await this.sshCommandService.executeCommand(
        sshServerId,
        command,
        executedBy,
        {
          ...options,
          jobId: job.id,
        },
      );

      await job.updateProgress(100);

      this.logger.log(`Hoàn thành SSH command job: ${job.id} - ${result.success ? 'Success' : 'Failed'}`);

      return {
        success: result.success,
        logId,
        result,
      };
    } catch (error) {
      this.logger.error(`Lỗi xử lý SSH command job ${job.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job thực thi batch commands
   */
  @Processor(SshExecutionJobName.EXECUTE_BATCH_COMMANDS)
  async handleExecuteBatchCommands(job: Job<any>) {
    const { sshServerId, commands, executedBy, options } = job.data;
    
    this.logger.log(`Bắt đầu xử lý SSH batch commands job: ${job.id}`);

    try {
      await job.updateProgress(10);

      const { result, logIds } = await this.sshCommandService.executeBatchCommands(
        sshServerId,
        commands,
        executedBy,
        {
          ...options,
          jobId: job.id,
        },
      );

      // Cập nhật progress dựa trên số lệnh đã hoàn thành
      const progressStep = 80 / commands.length;
      for (let i = 0; i < result.results.length; i++) {
        await job.updateProgress(10 + (i + 1) * progressStep);
      }

      await job.updateProgress(100);

      this.logger.log(`Hoàn thành SSH batch commands job: ${job.id} - ${result.successCount}/${result.totalCommands} thành công`);

      return {
        success: result.overallSuccess,
        logIds,
        result,
      };
    } catch (error) {
      this.logger.error(`Lỗi xử lý SSH batch commands job ${job.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job health check SSH server
   */
  @Processor(SshExecutionJobName.HEALTH_CHECK)
  async handleHealthCheck(job: Job<any>) {
    const { sshServerId, executedBy } = job.data;
    
    this.logger.log(`Bắt đầu xử lý SSH health check job: ${job.id}`);

    try {
      await job.updateProgress(20);

      // Thực hiện health check commands
      const healthCheckCommands = [
        { command: 'uptime', description: 'System uptime' },
        { command: 'free -h', description: 'Memory usage' },
        { command: 'df -h /', description: 'Disk usage' },
        { command: 'cat /proc/loadavg', description: 'Load average' },
      ];

      const { result, logIds } = await this.sshCommandService.executeBatchCommands(
        sshServerId,
        healthCheckCommands,
        executedBy,
        {
          executionType: 'health_check',
          jobId: job.id,
          metadata: {
            healthCheck: true,
            source: 'queue',
          },
        },
      );

      await job.updateProgress(100);

      this.logger.log(`Hoàn thành SSH health check job: ${job.id} - ${result.overallSuccess ? 'Healthy' : 'Unhealthy'}`);

      return {
        success: result.overallSuccess,
        logIds,
        healthStatus: result.overallSuccess ? 'healthy' : 'unhealthy',
        result,
      };
    } catch (error) {
      this.logger.error(`Lỗi xử lý SSH health check job ${job.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Event handler khi job hoàn thành
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job, result: any) {
    this.logger.log(`SSH execution job ${job.id} hoàn thành: ${JSON.stringify(result)}`);
  }

  /**
   * Event handler khi job thất bại
   */
  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`SSH execution job ${job.id} thất bại: ${error.message}`, error.stack);
  }

  /**
   * Event handler khi job bị stalled
   */
  @OnWorkerEvent('stalled')
  onStalled(job: Job) {
    this.logger.warn(`SSH execution job ${job.id} bị stalled`);
  }

  /**
   * Event handler khi job progress được cập nhật
   */
  @OnWorkerEvent('progress')
  onProgress(job: Job, progress: number) {
    this.logger.debug(`SSH execution job ${job.id} progress: ${progress}%`);
  }
}
