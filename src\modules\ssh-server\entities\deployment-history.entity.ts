import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';

/**
 * Entity đại diện cho lịch sử deployment
 * Lưu trữ thông tin các lần deployment dự án
 */
@Entity('deployment_histories')
@Index(['tenantId'])
@Index(['sshServerId'])
@Index(['deployedBy'])
@Index(['startedAt'])
@Index(['tenantId', 'sshServerId'])
@Index(['tenantId', 'projectName'])
@Index(['status'])
@Index(['deploymentType'])
export class DeploymentHistory {
  /**
   * ID duy nhất của deployment
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID tenant để phân tách dữ liệu
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: false })
  tenantId: number;

  /**
   * ID server SSH
   */
  @Column({ name: 'ssh_server_id', type: 'integer', nullable: false })
  sshServerId: number;

  /**
   * Tên dự án
   */
  @Column({ name: 'project_name', type: 'varchar', length: 255, nullable: false })
  projectName: string;

  /**
   * Repository URL
   */
  @Column({ name: 'repository_url', type: 'varchar', length: 500, nullable: true })
  repositoryUrl: string | null;

  /**
   * Branch được deploy
   */
  @Column({ name: 'branch', type: 'varchar', length: 255, nullable: true })
  branch: string | null;

  /**
   * Commit hash
   */
  @Column({ name: 'commit_hash', type: 'varchar', length: 255, nullable: true })
  commitHash: string | null;

  /**
   * Tag version
   */
  @Column({ name: 'tag', type: 'varchar', length: 255, nullable: true })
  tag: string | null;

  /**
   * Loại deployment
   */
  @Column({ 
    name: 'deployment_type', 
    type: 'varchar', 
    length: 50, 
    nullable: false 
  })
  deploymentType: 'git_pull' | 'git_clone' | 'docker_build' | 'npm_build' | 'custom' | 'rollback';

  /**
   * Môi trường deployment
   */
  @Column({ 
    name: 'environment', 
    type: 'varchar', 
    length: 50, 
    default: 'production' 
  })
  environment: 'development' | 'staging' | 'production' | 'testing';

  /**
   * Trạng thái deployment
   */
  @Column({ 
    name: 'status', 
    type: 'varchar', 
    length: 50, 
    default: 'pending',
    nullable: false 
  })
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'rollback';

  /**
   * Thư mục dự án trên server
   */
  @Column({ name: 'project_path', type: 'varchar', length: 500, nullable: true })
  projectPath: string | null;

  /**
   * Script build được sử dụng
   */
  @Column({ name: 'build_script', type: 'text', nullable: true })
  buildScript: string | null;

  /**
   * Script post-deployment
   */
  @Column({ name: 'post_deploy_script', type: 'text', nullable: true })
  postDeployScript: string | null;

  /**
   * Cấu hình deployment (JSON string)
   */
  @Column({ name: 'deployment_config', type: 'text', nullable: true })
  deploymentConfig: string | null;

  /**
   * Log deployment
   */
  @Column({ name: 'logs', type: 'text', nullable: true })
  logs: string | null;

  /**
   * Lỗi deployment
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string | null;

  /**
   * ID job queue
   */
  @Column({ name: 'job_id', type: 'varchar', length: 255, nullable: true })
  jobId: string | null;

  /**
   * Thời gian deployment trước đó (để rollback)
   */
  @Column({ name: 'previous_deployment_id', type: 'integer', nullable: true })
  previousDeploymentId: number | null;

  /**
   * Metadata bổ sung (JSON string)
   */
  @Column({ name: 'metadata', type: 'text', nullable: true })
  metadata: string | null;

  /**
   * ID người deploy
   */
  @Column({ name: 'deployed_by', type: 'integer', nullable: false })
  deployedBy: number;

  /**
   * Thời gian bắt đầu deployment (timestamp)
   */
  @Column({ name: 'started_at', type: 'bigint', nullable: false })
  startedAt: number;

  /**
   * Thời gian hoàn thành deployment (timestamp)
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  /**
   * Thời gian tạo record (timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;
}
