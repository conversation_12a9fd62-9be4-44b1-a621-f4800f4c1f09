import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max,
  <PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TodoPriority } from '../../enum/todo-priority.enum';

/**
 * DTO cho tạo công việc mới
 */
export class CreateTodoDto {
  /**
   * Tiêu đề công việc
   * @example "Thiết kế giao diện người dùng"
   */
  @ApiProperty({
    description: 'Tiêu đề công việc',
    example: 'Thiết kế giao diện người dùng',
    required: true,
  })
  @IsNotEmpty({ message: 'Tiêu đề không được để trống' })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  @MaxLength(255, { message: 'Tiêu đề không được vượt quá 255 ký tự' })
  title: string;

  /**
   * <PERSON><PERSON> tả chi tiết công việc
   * @example "Thiết kế giao diện người dùng cho trang chủ và trang sản phẩm"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết công việc',
    example: 'Thiết kế giao diện người dùng cho trang chủ và trang sản phẩm',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * ID của người được giao công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người được giao công việc',
    example: 1,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsInt({ message: 'ID người được giao phải là số nguyên' })
  @Min(1, { message: 'ID người được giao phải lớn hơn 0' })
  assigneeId?: number;

  /**
   * ID của dự án
   * @example 1
   */
  @ApiProperty({
    description: 'ID của dự án',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'ID dự án phải là số nguyên' })
  @Min(1, { message: 'ID dự án phải lớn hơn 0' })
  categoryId?: number;

  /**
   * ID của công việc cha (nếu là công việc con)
   * @example 2
   */
  @ApiProperty({
    description: 'ID của công việc cha (nếu là công việc con)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'ID công việc cha phải là số nguyên' })
  @Min(1, { message: 'ID công việc cha phải lớn hơn 0' })
  parentId?: number;

  /**
   * Mức độ ưu tiên của công việc
   * @example "medium"
   */
  @ApiProperty({
    description: 'Mức độ ưu tiên của công việc',
    enum: TodoPriority,
    example: TodoPriority.MEDIUM,
    required: false,
  })
  @IsOptional()
  @IsEnum(TodoPriority, { message: 'Mức độ ưu tiên không hợp lệ' })
  priority?: TodoPriority;

  /**
   * Số sao kỳ vọng (1-5)
   * @example 3
   */
  @ApiProperty({
    description: 'Số sao kỳ vọng (1-5)',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Số sao kỳ vọng phải là số nguyên' })
  @Min(1, { message: 'Số sao kỳ vọng phải từ 1 đến 5' })
  @Max(5, { message: 'Số sao kỳ vọng phải từ 1 đến 5' })
  expectedStars?: number;

  /**
   * Danh sách ID của các key result liên kết với công việc
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của các key result liên kết với công việc',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Danh sách key result phải là mảng' })
  @Type(() => Number)
  @IsInt({ each: true, message: 'ID key result phải là số nguyên' })
  @Min(1, { each: true, message: 'ID key result phải lớn hơn 0' })
  keyResultIds?: number[];

  /**
   * Thời gian bắt đầu làm việc (timestamp trong milliseconds)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu làm việc (timestamp trong milliseconds)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Thời gian bắt đầu phải là số nguyên (timestamp)' })
  startedAt?: number;

  /**
   * Thời gian deadline (timestamp trong milliseconds)
   * @example 1703980800000
   */
  @ApiProperty({
    description: 'Thời gian deadline (timestamp trong milliseconds)',
    example: 1703980800000,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Deadline phải là số nguyên (timestamp)' })
  @Min(Date.now(), { message: 'Deadline phải là thời gian trong tương lai' })
  deadline?: number;
}
