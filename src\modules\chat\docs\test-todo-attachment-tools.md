# Test Todo Attachment Tools - Multi-Agent System

## 🧪 Test Cases cho Tool Mới

### Test 1: <PERSON><PERSON><PERSON>t <PERSON> Tệp <PERSON> (Không Bộ Lọc)

**Tool:** `get_all_attachments_no_pagination`

**Input:**
```json
{}
```

**Expected Output:**
```
<PERSON><PERSON><PERSON> tất cả X tệp đính kèm thành công (không phân trang):
[
  {
    "id": 1,
    "todoId": 150,
    "filename": "document1.pdf",
    "url": "https://s3.../document1.pdf",
    "contentType": "application/pdf",
    "size": 1024000,
    "createdAt": 1703123456789,
    "createdBy": 1
  },
  ...
]
```

### Test 2: L<PERSON><PERSON>ệp <PERSON>h <PERSON>m Theo Todo ID

**Tool:** `get_all_attachments_no_pagination`

**Input:**
```json
{
  "todoId": 150
}
```

**Expected Output:**
```
<PERSON><PERSON><PERSON> tất cả X tệp đính kèm thành công (không phân trang):
[
  {
    "id": 1,
    "todoId": 150,
    "filename": "task_document.pdf",
    ...
  }
]
```

### Test 3: Tìm Kiếm Tệp Theo Tên

**Tool:** `get_all_attachments_no_pagination`

**Input:**
```json
{
  "search": "pdf",
  "sortBy": "filename",
  "sortDirection": "ASC"
}
```

**Expected Output:**
```
Lấy tất cả X tệp đính kèm thành công (không phân trang):
[
  {
    "filename": "document1.pdf",
    ...
  },
  {
    "filename": "report.pdf",
    ...
  }
]
```

### Test 4: Lọc Theo Người Tạo

**Tool:** `get_all_attachments_no_pagination`

**Input:**
```json
{
  "createdBy": 1,
  "sortBy": "createdAt",
  "sortDirection": "DESC"
}
```

### Test 5: Kết Hợp Nhiều Bộ Lọc

**Tool:** `get_all_attachments_no_pagination`

**Input:**
```json
{
  "todoId": 150,
  "search": "document",
  "createdBy": 1,
  "sortBy": "size",
  "sortDirection": "DESC"
}
```

## 🔄 So Sánh với Tool Cũ

### Test 6: So Sánh Performance

**Tool Cũ (Có Phân Trang):** `get_all_attachments_paginated`
```json
{
  "page": 1,
  "limit": 1000,
  "todoId": 150
}
```

**Tool Mới (Không Phân Trang):** `get_all_attachments_no_pagination`
```json
{
  "todoId": 150
}
```

**Expected:** Tool mới nhanh hơn và đơn giản hơn.

## 🎯 Use Case Tests

### Test 7: Export Scenario

**Scenario:** Agent cần export tất cả tệp đính kèm để tạo báo cáo.

**Tool:** `get_all_attachments_no_pagination`
```json
{
  "sortBy": "createdAt",
  "sortDirection": "DESC"
}
```

**Validation:**
- Trả về tất cả tệp đính kèm
- Không có pagination metadata
- Sắp xếp theo thời gian tạo mới nhất

### Test 8: Dropdown Scenario

**Scenario:** Agent cần tạo dropdown list tệp đính kèm cho một công việc.

**Tool:** `get_all_attachments_no_pagination`
```json
{
  "todoId": 150,
  "sortBy": "filename",
  "sortDirection": "ASC"
}
```

**Validation:**
- Chỉ tệp của todo 150
- Sắp xếp theo tên file A-Z
- Format phù hợp cho dropdown

### Test 9: Statistics Scenario

**Scenario:** Agent cần thống kê tệp đính kèm theo người tạo.

**Tool:** `get_all_attachments_no_pagination`
```json
{
  "createdBy": 1
}
```

**Validation:**
- Chỉ tệp của user ID 1
- Có thể đếm số lượng và tính tổng size

## ⚠️ Error Cases

### Test 10: Invalid TodoId

**Input:**
```json
{
  "todoId": 99999
}
```

**Expected Output:**
```
Lấy tất cả 0 tệp đính kèm thành công (không phân trang):
[]
```

### Test 11: Invalid CreatedBy

**Input:**
```json
{
  "createdBy": 99999
}
```

**Expected Output:**
```
Lấy tất cả 0 tệp đính kèm thành công (không phân trang):
[]
```

### Test 12: Invalid SortBy

**Input:**
```json
{
  "sortBy": "invalid_field"
}
```

**Expected:** Có thể gây lỗi database, cần handle gracefully.

## 🔧 Integration Tests

### Test 13: Với Các Tool Khác

**Workflow:**
1. `create_todo` - Tạo công việc mới
2. `create_attachment_upload_url` - Tạo URL upload
3. `confirm_attachment_upload` - Xác nhận upload
4. `get_all_attachments_no_pagination` - Lấy tất cả tệp

**Validation:** Tool mới phải trả về tệp vừa upload.

### Test 14: Tenant Isolation

**Setup:** Có 2 tenant khác nhau với dữ liệu riêng.

**Test:** Tool chỉ trả về tệp đính kèm của tenant hiện tại.

## 📊 Performance Tests

### Test 15: Large Dataset

**Setup:** Tạo 1000+ tệp đính kèm.

**Tool:** `get_all_attachments_no_pagination`
```json
{}
```

**Validation:**
- Response time < 5 seconds
- Memory usage reasonable
- Không timeout

### Test 16: With Filters on Large Dataset

**Tool:** `get_all_attachments_no_pagination`
```json
{
  "search": "pdf",
  "sortBy": "size",
  "sortDirection": "DESC"
}
```

**Validation:** Bộ lọc hoạt động hiệu quả trên dataset lớn.

## ✅ Success Criteria

1. **Functionality:** Tất cả test cases pass
2. **Performance:** Response time < 5s cho 1000+ records
3. **Accuracy:** Kết quả chính xác với bộ lọc
4. **Security:** Tenant isolation hoạt động đúng
5. **Reliability:** Không có memory leak hoặc timeout
