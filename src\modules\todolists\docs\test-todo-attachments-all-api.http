### Test Todo Attachments All API
### C<PERSON>n thay đổi {{baseUrl}} và {{authToken}} phù hợp với environment

@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### ===== TODO ATTACHMENTS APIs =====

### 1. <PERSON><PERSON><PERSON> danh sách tệp đính kèm có phân trang (API cũ)
GET {{baseUrl}}/v1/todo-attachments?page=1&limit=10
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 2. Lấy TẤT CẢ danh sách tệp đính kèm không phân trang (API mới)
GET {{baseUrl}}/v1/todo-attachments/all
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 3. L<PERSON>y tất cả tệp đính kèm với bộ lọc theo todoId
GET {{baseUrl}}/v1/todo-attachments/all?todoId=150
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 4. <PERSON><PERSON><PERSON> tất cả tệp đính kèm với bộ lọc theo createdBy
GET {{baseUrl}}/v1/todo-attachments/all?createdBy=1
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 5. Lấy tất cả tệp đính kèm với tìm kiếm theo tên file
GET {{baseUrl}}/v1/todo-attachments/all?search=document
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 6. Lấy tất cả tệp đính kèm với sắp xếp
GET {{baseUrl}}/v1/todo-attachments/all?sortBy=filename&sortDirection=ASC
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 7. Lấy tất cả tệp đính kèm với nhiều bộ lọc kết hợp
GET {{baseUrl}}/v1/todo-attachments/all?todoId=150&search=pdf&sortBy=createdAt&sortDirection=DESC
Authorization: Bearer {{authToken}}
Content-Type: application/json

### ===== SO SÁNH VỚI API CŨ =====

### 8. API cũ với phân trang (để so sánh)
GET {{baseUrl}}/v1/todo-attachments?page=1&limit=1000&todoId=150
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 9. API mới không phân trang (để so sánh)
GET {{baseUrl}}/v1/todo-attachments/all?todoId=150
Authorization: Bearer {{authToken}}
Content-Type: application/json

### ===== KIỂM TRA PERFORMANCE =====

### 10. Lấy tất cả không có bộ lọc (có thể nhiều dữ liệu)
GET {{baseUrl}}/v1/todo-attachments/all
Authorization: Bearer {{authToken}}
Content-Type: application/json

### 11. Lấy tệp đính kèm của một todo cụ thể (API khác)
GET {{baseUrl}}/v1/todo-attachments/by-todo/150
Authorization: Bearer {{authToken}}
Content-Type: application/json
