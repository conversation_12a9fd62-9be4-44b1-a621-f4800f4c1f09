# SSH Server Module Testing Guide

## Overview

Hướng dẫn testing cho SSH Server Management Module bao gồm unit tests, integration tests và manual testing.

## Test Structure

```
src/modules/ssh-server/tests/
├── ssh-server.service.spec.ts          # Unit tests cho SSH Server Service
├── ssh-command.service.spec.ts         # Unit tests cho SSH Command Service
├── deployment.service.spec.ts          # Unit tests cho Deployment Service
├── ssh-execution.integration.spec.ts   # Integration tests cho API endpoints
└── fixtures/                           # Test data và mock objects
```

## Running Tests

### Unit Tests

```bash
# Chạy tất cả unit tests cho SSH module
npm run test src/modules/ssh-server

# Chạy specific test file
npm run test src/modules/ssh-server/tests/ssh-server.service.spec.ts

# Chạy tests với coverage
npm run test:cov src/modules/ssh-server

# Watch mode cho development
npm run test:watch src/modules/ssh-server
```

### Integration Tests

```bash
# Chạy integration tests
npm run test:e2e ssh-server

# Chạy với database setup
npm run test:e2e:db ssh-server
```

### All Tests

```bash
# Chạy tất cả tests
npm run test:all:ssh-server
```

## Test Categories

### 1. Unit Tests

#### SSH Server Service Tests
- ✅ Create SSH server with encryption
- ✅ Find SSH server by ID
- ✅ Update SSH server information
- ✅ Test SSH connection
- ✅ Soft delete SSH server
- ✅ Get statistics
- ✅ Activate/deactivate server
- ✅ Error handling

#### SSH Command Service Tests
- ✅ Execute single command
- ✅ Execute batch commands
- ✅ Handle command failures
- ✅ Connection error handling
- ✅ Timeout handling
- ✅ Get common commands
- ✅ Get deployment templates

#### Deployment Service Tests
- ✅ Deploy project successfully
- ✅ Handle deployment failures
- ✅ Rollback deployment
- ✅ Validate deployment config
- ✅ Get deployment statistics
- ✅ Generate deployment commands

### 2. Integration Tests

#### API Endpoint Tests
- ✅ SSH Server CRUD operations
- ✅ SSH command execution
- ✅ Batch command execution
- ✅ Deployment operations
- ✅ Authentication & authorization
- ✅ Input validation
- ✅ Error handling

#### Database Integration
- ✅ Entity relationships
- ✅ Tenant isolation
- ✅ Soft delete functionality
- ✅ Audit logging

### 3. End-to-End Tests

#### Complete Workflows
- ✅ Create server → Test connection → Execute commands
- ✅ Setup project → Deploy → Rollback
- ✅ Batch operations with error handling
- ✅ Queue processing

## Test Data Setup

### Mock SSH Server
```typescript
const mockSshServer = {
  id: 1,
  name: 'Test Server',
  host: '*************',
  port: 22,
  username: 'ubuntu',
  passwordEncrypted: 'encrypted-password',
  description: 'Test SSH server',
  isActive: true,
  connectionTimeout: 30000,
  commandTimeout: 300000,
  retryAttempts: 3,
  retryDelay: 5000,
  createdBy: 1,
  tenantId: 1,
  createdAt: Date.now(),
  updatedAt: Date.now(),
};
```

### Mock Command Result
```typescript
const mockCommandResult = {
  command: 'ls -la',
  stdout: 'total 8\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 .',
  stderr: '',
  exitCode: 0,
  executionTime: 1500,
  success: true,
};
```

### Mock Deployment Config
```typescript
const mockDeploymentConfig = {
  projectName: 'test-project',
  deploymentType: 'git_pull',
  environment: 'production',
  projectPath: '/home/<USER>/test-project',
  branch: 'main',
  buildScript: 'npm run build',
  postDeployScript: 'pm2 restart app',
};
```

## Testing Best Practices

### 1. Unit Test Guidelines

```typescript
describe('SshServerService', () => {
  let service: SshServerService;
  let mockRepository: jest.Mocked<SshServerRepository>;

  beforeEach(async () => {
    // Setup test module với mocked dependencies
    const module = await Test.createTestingModule({
      providers: [
        SshServerService,
        { provide: SshServerRepository, useValue: mockRepository },
      ],
    }).compile();

    service = module.get<SshServerService>(SshServerService);
  });

  it('should create SSH server successfully', async () => {
    // Arrange
    mockRepository.findByName.mockResolvedValue(null);
    mockRepository.create.mockResolvedValue(mockSshServer);

    // Act
    const result = await service.create(createDto, userId);

    // Assert
    expect(result).toEqual(mockSshServer);
    expect(mockRepository.create).toHaveBeenCalledWith(
      expect.objectContaining({ name: 'Test Server' })
    );
  });
});
```

### 2. Integration Test Guidelines

```typescript
describe('SSH Server API Integration', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    // Setup test application
    const moduleFixture = await Test.createTestingModule({
      imports: [SshServerModule, TestDatabaseModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token
    authToken = await getTestAuthToken();
  });

  it('should create SSH server via API', async () => {
    const response = await request(app.getHttpServer())
      .post('/ssh-servers')
      .set('Authorization', `Bearer ${authToken}`)
      .send(createServerDto)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.name).toBe('Test Server');
  });
});
```

### 3. Mock Strategy

#### Service Layer Mocks
```typescript
const mockSshConnectionService = {
  createConnection: jest.fn(),
  executeCommand: jest.fn(),
  closeConnection: jest.fn(),
  healthCheck: jest.fn(),
};
```

#### Repository Layer Mocks
```typescript
const mockSshServerRepository = {
  create: jest.fn(),
  findById: jest.fn(),
  findWithPagination: jest.fn(),
  update: jest.fn(),
  softDelete: jest.fn(),
  getStatistics: jest.fn(),
};
```

## Test Coverage Requirements

### Minimum Coverage Targets
- **Unit Tests**: 90% line coverage
- **Integration Tests**: 80% endpoint coverage
- **Critical Paths**: 100% coverage

### Coverage Areas
- ✅ Service methods
- ✅ Repository operations
- ✅ API endpoints
- ✅ Error handling
- ✅ Validation logic
- ✅ Security features

## Manual Testing

### 1. SSH Connection Testing

```bash
# Test với real SSH server
export SSH_TEST_HOST="your-test-server.com"
export SSH_TEST_USER="testuser"
export SSH_TEST_PASS="testpass"

npm run test:manual:ssh-connection
```

### 2. Command Execution Testing

```bash
# Test command execution
curl -X POST http://localhost:3000/api/ssh-execution/command \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sshServerId": 1,
    "command": "echo Hello World"
  }'
```

### 3. Deployment Testing

```bash
# Test deployment workflow
curl -X POST http://localhost:3000/api/ssh-execution/deploy \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sshServerId": 1,
    "projectName": "test-app",
    "deploymentType": "git_pull",
    "environment": "staging",
    "projectPath": "/tmp/test-app"
  }'
```

## Performance Testing

### Load Testing Commands

```bash
# Test concurrent SSH connections
npm run test:load:ssh-connections

# Test batch command performance
npm run test:load:batch-commands

# Test deployment under load
npm run test:load:deployments
```

### Performance Benchmarks
- SSH connection establishment: < 2 seconds
- Single command execution: < 5 seconds
- Batch commands (10 commands): < 30 seconds
- Deployment (typical): < 5 minutes

## Security Testing

### 1. Authentication Tests
- ✅ Unauthorized access prevention
- ✅ JWT token validation
- ✅ Tenant isolation verification

### 2. Input Validation Tests
- ✅ SQL injection prevention
- ✅ Command injection prevention
- ✅ XSS prevention
- ✅ Path traversal prevention

### 3. Encryption Tests
- ✅ Password encryption/decryption
- ✅ Private key encryption
- ✅ Secure credential storage

## Troubleshooting Tests

### Common Test Issues

#### 1. SSH Connection Failures
```typescript
// Mock SSH connection failure
mockConnectionService.createConnection.mockRejectedValue(
  new Error('ECONNREFUSED')
);

// Test error handling
const result = await service.executeCommand(1, 'test', 1);
expect(result.success).toBe(false);
expect(result.error).toContain('ECONNREFUSED');
```

#### 2. Database Connection Issues
```typescript
// Test database unavailability
mockRepository.create.mockRejectedValue(
  new Error('Database connection failed')
);
```

#### 3. Timeout Handling
```typescript
// Test command timeout
mockConnectionService.executeCommand.mockImplementation(
  () => new Promise(resolve => setTimeout(resolve, 35000))
);
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
name: SSH Module Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test src/modules/ssh-server
      
      - name: Run integration tests
        run: npm run test:e2e ssh-server
      
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

### Test Reports

Tests generate reports in:
- `coverage/` - Coverage reports
- `test-results/` - Test results XML
- `logs/` - Test execution logs

## Test Maintenance

### Regular Tasks
1. **Weekly**: Review test coverage reports
2. **Monthly**: Update test data và mocks
3. **Quarterly**: Performance benchmark review
4. **Release**: Full regression testing

### Test Data Cleanup
```bash
# Clean test databases
npm run test:cleanup

# Reset test fixtures
npm run test:fixtures:reset

# Clear test logs
npm run test:logs:clear
```

## Debugging Tests

### Debug Mode
```bash
# Run tests in debug mode
npm run test:debug src/modules/ssh-server

# Debug specific test
npm run test:debug -- --testNamePattern="should create SSH server"
```

### Logging
```typescript
// Enable debug logging in tests
process.env.LOG_LEVEL = 'debug';
process.env.TEST_DEBUG = 'true';
```

### Test Isolation
```typescript
// Ensure test isolation
afterEach(async () => {
  await cleanupTestData();
  jest.clearAllMocks();
});
```
