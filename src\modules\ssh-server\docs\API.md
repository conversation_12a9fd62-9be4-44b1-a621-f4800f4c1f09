# SSH Server Management API Documentation

## Overview

SSH Server Management API cung cấp các endpoint để quản lý SSH servers, thực thi lệnh từ xa và deployment automation.

## Authentication

Tất cả API endpoints yêu cầu JWT authentication:

```
Authorization: Bearer <jwt_token>
```

## Base URL

```
/api/ssh-servers
/api/ssh-execution
```

## SSH Server Management APIs

### 1. Create SSH Server

**POST** `/ssh-servers`

Tạo SSH server mới với thông tin kết nối được mã hóa.

**Request Body:**
```json
{
  "name": "Production Server 1",
  "host": "*************",
  "port": 22,
  "username": "ubuntu",
  "password": "secure-password",
  "description": "Production server for web application",
  "connectionTimeout": 30000,
  "commandTimeout": 300000,
  "retryAttempts": 3,
  "retryDelay": 5000,
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "SSH server đã được tạo thành công",
  "data": {
    "id": 1,
    "name": "Production Server 1",
    "host": "*************",
    "port": 22,
    "username": "ubuntu",
    "description": "Production server for web application",
    "isActive": true,
    "connectionTimeout": 30000,
    "commandTimeout": 300000,
    "retryAttempts": 3,
    "retryDelay": 5000,
    "createdBy": 1,
    "createdAt": 1640995200000,
    "updatedAt": 1640995200000
  }
}
```

### 2. Get SSH Servers List

**GET** `/ssh-servers`

Lấy danh sách SSH servers với phân trang và tìm kiếm.

**Query Parameters:**
- `page` (optional): Số trang (mặc định: 1)
- `limit` (optional): Số items per page (mặc định: 10)
- `search` (optional): Từ khóa tìm kiếm
- `isActive` (optional): Lọc theo trạng thái hoạt động

**Response:**
```json
{
  "success": true,
  "message": "Lấy danh sách SSH servers thành công",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "Production Server 1",
        "host": "*************",
        "port": 22,
        "username": "ubuntu",
        "isActive": true,
        "lastConnectionStatus": "connected",
        "lastConnectedAt": 1640995200000,
        "createdAt": 1640995200000
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

### 3. Get SSH Server by ID

**GET** `/ssh-servers/:id`

Lấy thông tin chi tiết SSH server theo ID.

**Response:**
```json
{
  "success": true,
  "message": "Lấy thông tin SSH server thành công",
  "data": {
    "id": 1,
    "name": "Production Server 1",
    "host": "*************",
    "port": 22,
    "username": "ubuntu",
    "description": "Production server for web application",
    "isActive": true,
    "connectionTimeout": 30000,
    "commandTimeout": 300000,
    "retryAttempts": 3,
    "retryDelay": 5000,
    "lastConnectionStatus": "connected",
    "lastConnectedAt": 1640995200000,
    "createdBy": 1,
    "createdAt": 1640995200000,
    "updatedAt": 1640995200000
  }
}
```

### 4. Update SSH Server

**PUT** `/ssh-servers/:id`

Cập nhật thông tin SSH server.

**Request Body:**
```json
{
  "description": "Updated description",
  "isActive": true
}
```

### 5. Delete SSH Server

**DELETE** `/ssh-servers/:id`

Xóa SSH server (soft delete).

**Response:**
```json
{
  "success": true,
  "message": "SSH server đã được xóa thành công",
  "data": null
}
```

### 6. Test SSH Connection

**POST** `/ssh-servers/:id/test-connection`

Kiểm tra kết nối SSH tới server và lấy thông tin hệ thống.

**Response:**
```json
{
  "success": true,
  "message": "Kết nối SSH thành công",
  "data": {
    "success": true,
    "responseTime": 1500,
    "message": "Kết nối SSH thành công",
    "systemInfo": {
      "uptime": "1 day, 2 hours",
      "memoryUsage": "2.1G/8.0G",
      "diskUsage": "45% used",
      "loadAverage": "0.15 0.20 0.18"
    },
    "testedAt": 1640995200000
  }
}
```

### 7. Get SSH Server Statistics

**GET** `/ssh-servers/statistics/overview`

Lấy thống kê tổng quan về SSH servers.

**Response:**
```json
{
  "success": true,
  "message": "Lấy thống kê SSH servers thành công",
  "data": {
    "total": 10,
    "active": 8,
    "inactive": 2,
    "connected": 6,
    "disconnected": 2
  }
}
```

## SSH Command Execution APIs

### 1. Execute Single Command

**POST** `/ssh-execution/command`

Thực thi lệnh SSH đơn lẻ trên server được chỉ định.

**Request Body:**
```json
{
  "sshServerId": 1,
  "command": "ls -la",
  "workingDirectory": "/home/<USER>",
  "environment": {
    "NODE_ENV": "production"
  },
  "timeout": 30000,
  "description": "List directory contents"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Lệnh thực thi thành công",
  "data": {
    "logId": 123,
    "command": "ls -la",
    "stdout": "total 24\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan 1 12:00 .",
    "stderr": "",
    "exitCode": 0,
    "executionTime": 1500,
    "success": true,
    "executedAt": 1640995200000
  }
}
```

### 2. Execute Batch Commands

**POST** `/ssh-execution/batch`

Thực thi nhiều lệnh SSH theo thứ tự trên server.

**Request Body:**
```json
{
  "sshServerId": 1,
  "commands": [
    {
      "command": "git pull",
      "description": "Pull latest code",
      "workingDirectory": "/home/<USER>/project"
    },
    {
      "command": "npm install",
      "description": "Install dependencies",
      "continueOnError": false
    },
    {
      "command": "npm run build",
      "description": "Build application"
    }
  ],
  "description": "Deploy latest version"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Batch commands thực thi thành công",
  "data": {
    "logIds": [123, 124, 125],
    "totalCommands": 3,
    "successCount": 3,
    "failureCount": 0,
    "results": [
      {
        "command": "git pull",
        "stdout": "Already up to date.",
        "stderr": "",
        "exitCode": 0,
        "executionTime": 2000,
        "success": true,
        "description": "Pull latest code"
      }
    ],
    "totalExecutionTime": 5000,
    "overallSuccess": true,
    "startedAt": 1640995200000,
    "completedAt": 1640995205000
  }
}
```

### 3. Deploy Project

**POST** `/ssh-execution/deploy`

Thực hiện deployment dự án lên server.

**Request Body:**
```json
{
  "sshServerId": 1,
  "projectName": "my-web-app",
  "deploymentType": "git_pull",
  "environment": "production",
  "projectPath": "/home/<USER>/my-web-app",
  "branch": "main",
  "buildScript": "npm run build:prod",
  "postDeployScript": "pm2 restart app",
  "environmentVariables": {
    "NODE_ENV": "production",
    "PORT": "3000"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Deployment thành công",
  "data": {
    "deploymentId": 456,
    "projectName": "my-web-app",
    "deploymentType": "git_pull",
    "environment": "production",
    "status": "completed",
    "branch": "main",
    "startedAt": 1640995200000,
    "completedAt": 1640995300000,
    "executionTime": 100000,
    "logs": "=== Command 1: Pull latest code ===\nExit Code: 0\nSTDOUT: Already up to date.",
    "executionLogIds": [127, 128, 129],
    "deployedBy": 1,
    "createdAt": 1640995200000
  }
}
```

### 4. Rollback Deployment

**POST** `/ssh-execution/rollback`

Rollback deployment về phiên bản trước đó.

**Request Body:**
```json
{
  "sshServerId": 1,
  "deploymentId": 456,
  "createBackupBeforeRollback": true
}
```

### 5. Deploy with Template

**POST** `/ssh-execution/deploy-template`

Thực hiện deployment sử dụng template có sẵn.

**Request Body:**
```json
{
  "sshServerId": 1,
  "templateName": "NODEJS",
  "projectName": "my-nodejs-app",
  "projectPath": "/home/<USER>/my-nodejs-app",
  "metadata": {
    "pm2AppName": "my-app"
  }
}
```

### 6. Get Execution Logs

**GET** `/ssh-execution/logs`

Lấy danh sách logs thực thi lệnh SSH với phân trang và lọc.

**Query Parameters:**
- `page` (optional): Số trang
- `limit` (optional): Số items per page
- `sshServerId` (optional): Lọc theo SSH server ID
- `status` (optional): Lọc theo trạng thái (pending, running, completed, failed)
- `executionType` (optional): Lọc theo loại thực thi (manual, batch, deployment)
- `search` (optional): Tìm kiếm trong command hoặc output

**Response:**
```json
{
  "success": true,
  "message": "Lấy danh sách execution logs thành công",
  "data": {
    "items": [
      {
        "id": 123,
        "sshServerId": 1,
        "command": "ls -la",
        "output": "total 24\ndrwxr-xr-x 3 <USER> <GROUP> 4096",
        "exitCode": 0,
        "executionTimeMs": 1500,
        "status": "completed",
        "executionType": "manual",
        "executedBy": 1,
        "executedAt": 1640995200000,
        "completedAt": 1640995202000
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

### 7. Get Deployment Templates

**GET** `/ssh-execution/deployment-templates`

Lấy danh sách các template deployment có sẵn.

**Response:**
```json
{
  "success": true,
  "message": "Lấy danh sách deployment templates thành công",
  "data": {
    "templates": {
      "NODEJS": {
        "name": "Node.js Deployment",
        "description": "Standard Node.js application deployment",
        "commands": [
          {
            "command": "git pull",
            "description": "Pull latest code"
          },
          {
            "command": "npm ci",
            "description": "Install dependencies"
          },
          {
            "command": "npm run build",
            "description": "Build application"
          },
          {
            "command": "pm2 restart app",
            "description": "Restart application"
          }
        ]
      }
    }
  }
}
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "errors": [
    {
      "field": "name",
      "message": "Tên server không được để trống"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized access",
  "data": null
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "SSH server với ID 999 không tồn tại",
  "data": null
}
```

### 409 Conflict
```json
{
  "success": false,
  "message": "SSH server với tên 'Production Server 1' đã tồn tại",
  "data": null
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Lỗi hệ thống",
  "data": null
}
```

## Rate Limiting

- SSH command execution: 100 requests per minute per user
- Deployment operations: 10 requests per minute per user
- Server management: 200 requests per minute per user

## Security Notes

1. **Credential Encryption**: Tất cả passwords và private keys được mã hóa trước khi lưu database
2. **Tenant Isolation**: Mỗi tenant chỉ có thể truy cập SSH servers của mình
3. **Audit Logging**: Tất cả operations được log để audit
4. **Connection Timeout**: Tự động đóng kết nối SSH sau timeout
5. **Input Validation**: Tất cả input được validate nghiêm ngặt

## Usage Examples

### Example 1: Create and Test SSH Server
```bash
# Create SSH server
curl -X POST /api/ssh-servers \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Server",
    "host": "*************",
    "username": "ubuntu",
    "password": "secure-password"
  }'

# Test connection
curl -X POST /api/ssh-servers/1/test-connection \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### Example 2: Execute Commands
```bash
# Single command
curl -X POST /api/ssh-execution/command \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sshServerId": 1,
    "command": "uptime"
  }'

# Batch commands
curl -X POST /api/ssh-execution/batch \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sshServerId": 1,
    "commands": [
      {"command": "git pull", "description": "Update code"},
      {"command": "npm install", "description": "Install deps"}
    ]
  }'
```

### Example 3: Deploy Project
```bash
curl -X POST /api/ssh-execution/deploy \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sshServerId": 1,
    "projectName": "my-app",
    "deploymentType": "git_pull",
    "environment": "production",
    "projectPath": "/home/<USER>/my-app",
    "branch": "main"
  }'
```
