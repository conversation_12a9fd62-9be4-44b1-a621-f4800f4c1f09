import { Injectable } from '@nestjs/common';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';
import { ToolRunnableConfig } from '@langchain/core/tools';
import { SshServerManagementTool } from './ssh-server-management.tool';
import { SshCommandExecutionTool } from './ssh-command-execution.tool';
import { DeploymentAutomationTool } from './deployment-automation.tool';

/**
 * Provider cung cấp SSH tools cho chat system
 */
@Injectable()
export class SshToolsProvider {
  constructor(
    private readonly sshServerManagementTool: SshServerManagementTool,
    private readonly sshCommandExecutionTool: SshCommandExecutionTool,
    private readonly deploymentAutomationTool: DeploymentAutomationTool,
  ) {}

  /**
   * Lấy tất cả SSH tools
   */
  getTools() {
    return [
      // SSH Server Management Tools
      ...this.getSshServerManagementTools(),
      
      // SSH Command Execution Tools
      ...this.getSshCommandExecutionTools(),
      
      // Deployment Automation Tools
      ...this.getDeploymentAutomationTools(),
    ];
  }

  /**
   * SSH Server Management Tools
   */
  private getSshServerManagementTools() {
    return [
      // Lấy danh sách SSH servers
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const result = await this.sshServerManagementTool.listSshServers({
              page: _args.page,
              limit: _args.limit,
              search: _args.search,
              isActive: _args.isActive,
            });
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi lấy danh sách SSH servers: ${error.message}`;
          }
        },
        {
          name: 'list_ssh_servers',
          description: 'Lấy danh sách SSH servers với phân trang và tìm kiếm',
          schema: z.object({
            page: z.number().optional().describe('Số trang (mặc định: 1)'),
            limit: z.number().optional().describe('Số items per page (mặc định: 10)'),
            search: z.string().optional().describe('Từ khóa tìm kiếm'),
            isActive: z.boolean().optional().describe('Lọc theo trạng thái hoạt động'),
          }),
        }
      ),

      // Lấy thông tin SSH server theo ID
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const result = await this.sshServerManagementTool.getSshServerById(_args.serverId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi lấy thông tin SSH server: ${error.message}`;
          }
        },
        {
          name: 'get_ssh_server_by_id',
          description: 'Lấy thông tin chi tiết SSH server theo ID',
          schema: z.object({
            serverId: z.number().describe('ID của SSH server'),
          }),
        }
      ),

      // Tạo SSH server mới
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const result = await this.sshServerManagementTool.createSshServer({
              name: _args.name,
              host: _args.host,
              port: _args.port,
              username: _args.username,
              password: _args.password,
              privateKey: _args.privateKey,
              passphrase: _args.passphrase,
              description: _args.description,
              connectionTimeout: _args.connectionTimeout,
              commandTimeout: _args.commandTimeout,
              retryAttempts: _args.retryAttempts,
              retryDelay: _args.retryDelay,
              isActive: _args.isActive,
            }, userId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi tạo SSH server: ${error.message}`;
          }
        },
        {
          name: 'create_ssh_server',
          description: 'Tạo SSH server mới với thông tin kết nối',
          schema: z.object({
            name: z.string().describe('Tên SSH server (duy nhất)'),
            host: z.string().describe('Địa chỉ IP hoặc hostname'),
            port: z.number().optional().describe('Port SSH (mặc định: 22)'),
            username: z.string().describe('Username để đăng nhập SSH'),
            password: z.string().optional().describe('Password để đăng nhập SSH'),
            privateKey: z.string().optional().describe('Private key để đăng nhập SSH'),
            passphrase: z.string().optional().describe('Passphrase cho private key'),
            description: z.string().optional().describe('Mô tả server'),
            connectionTimeout: z.number().optional().describe('Timeout kết nối (ms)'),
            commandTimeout: z.number().optional().describe('Timeout lệnh (ms)'),
            retryAttempts: z.number().optional().describe('Số lần thử lại'),
            retryDelay: z.number().optional().describe('Delay thử lại (ms)'),
            isActive: z.boolean().optional().describe('Trạng thái hoạt động'),
          }),
        }
      ),

      // Test kết nối SSH
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const result = await this.sshServerManagementTool.testSshConnection(_args.serverId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi test kết nối SSH: ${error.message}`;
          }
        },
        {
          name: 'test_ssh_connection',
          description: 'Test kết nối SSH tới server và lấy thông tin hệ thống',
          schema: z.object({
            serverId: z.number().describe('ID của SSH server cần test'),
          }),
        }
      ),

      // Lấy thống kê SSH servers
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const result = await this.sshServerManagementTool.getSshServerStatistics();
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi lấy thống kê SSH servers: ${error.message}`;
          }
        },
        {
          name: 'get_ssh_server_statistics',
          description: 'Lấy thống kê tổng quan về SSH servers (tổng số, hoạt động, kết nối)',
          schema: z.object({}),
        }
      ),
    ];
  }

  /**
   * SSH Command Execution Tools
   */
  private getSshCommandExecutionTools() {
    return [
      // Thực thi lệnh SSH đơn lẻ
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const result = await this.sshCommandExecutionTool.executeCommand({
              sshServerId: _args.sshServerId,
              command: _args.command,
              workingDirectory: _args.workingDirectory,
              environment: _args.environment,
              timeout: _args.timeout,
              description: _args.description,
            }, userId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi thực thi lệnh SSH: ${error.message}`;
          }
        },
        {
          name: 'execute_ssh_command',
          description: 'Thực thi lệnh SSH đơn lẻ trên server được chỉ định',
          schema: z.object({
            sshServerId: z.number().describe('ID của SSH server'),
            command: z.string().describe('Lệnh cần thực thi'),
            workingDirectory: z.string().optional().describe('Thư mục làm việc'),
            environment: z.record(z.string()).optional().describe('Biến môi trường'),
            timeout: z.number().optional().describe('Timeout thực thi (ms)'),
            description: z.string().optional().describe('Mô tả lệnh'),
          }),
        }
      ),

      // Thực thi batch commands
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const result = await this.sshCommandExecutionTool.executeBatchCommands({
              sshServerId: _args.sshServerId,
              commands: _args.commands,
              description: _args.description,
            }, userId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi thực thi batch commands: ${error.message}`;
          }
        },
        {
          name: 'execute_batch_ssh_commands',
          description: 'Thực thi nhiều lệnh SSH theo thứ tự trên server',
          schema: z.object({
            sshServerId: z.number().describe('ID của SSH server'),
            commands: z.array(z.object({
              command: z.string().describe('Lệnh cần thực thi'),
              description: z.string().optional().describe('Mô tả lệnh'),
              workingDirectory: z.string().optional().describe('Thư mục làm việc'),
              environment: z.record(z.string()).optional().describe('Biến môi trường'),
              continueOnError: z.boolean().optional().describe('Tiếp tục nếu lệnh thất bại'),
            })).describe('Danh sách lệnh cần thực thi'),
            description: z.string().optional().describe('Mô tả batch commands'),
          }),
        }
      ),

      // Lấy danh sách execution logs
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const result = await this.sshCommandExecutionTool.getExecutionLogs({
              page: _args.page,
              limit: _args.limit,
              sshServerId: _args.sshServerId,
              status: _args.status,
              executionType: _args.executionType,
              search: _args.search,
            }, userId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi lấy execution logs: ${error.message}`;
          }
        },
        {
          name: 'get_ssh_execution_logs',
          description: 'Lấy danh sách logs thực thi lệnh SSH với phân trang và lọc',
          schema: z.object({
            page: z.number().optional().describe('Số trang'),
            limit: z.number().optional().describe('Số items per page'),
            sshServerId: z.number().optional().describe('Lọc theo SSH server ID'),
            status: z.string().optional().describe('Lọc theo trạng thái (pending, running, completed, failed)'),
            executionType: z.string().optional().describe('Lọc theo loại thực thi (manual, batch, deployment)'),
            search: z.string().optional().describe('Tìm kiếm trong command hoặc output'),
          }),
        }
      ),

      // Lấy danh sách lệnh phổ biến
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const result = await this.sshCommandExecutionTool.getCommonCommands(_args.category);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi lấy danh sách lệnh: ${error.message}`;
          }
        },
        {
          name: 'get_common_ssh_commands',
          description: 'Lấy danh sách lệnh SSH phổ biến theo category (SYSTEM, GIT, NODE, DOCKER, PM2, SERVICE, FILE)',
          schema: z.object({
            category: z.string().optional().describe('Category lệnh (SYSTEM, GIT, NODE, DOCKER, PM2, SERVICE, FILE)'),
          }),
        }
      ),
    ];
  }

  /**
   * Deployment Automation Tools
   */
  private getDeploymentAutomationTools() {
    return [
      // Deploy dự án
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const result = await this.deploymentAutomationTool.deployProject({
              sshServerId: _args.sshServerId,
              projectName: _args.projectName,
              deploymentType: _args.deploymentType,
              environment: _args.environment,
              projectPath: _args.projectPath,
              repositoryUrl: _args.repositoryUrl,
              branch: _args.branch,
              commitHash: _args.commitHash,
              tag: _args.tag,
              buildScript: _args.buildScript,
              postDeployScript: _args.postDeployScript,
              environmentVariables: _args.environmentVariables,
              additionalConfig: _args.additionalConfig,
              customCommands: _args.customCommands,
            }, userId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi deploy dự án: ${error.message}`;
          }
        },
        {
          name: 'deploy_project',
          description: 'Deploy dự án lên server với các loại deployment khác nhau (git_pull, git_clone, docker_build, npm_build, custom)',
          schema: z.object({
            sshServerId: z.number().describe('ID của SSH server'),
            projectName: z.string().describe('Tên dự án'),
            deploymentType: z.enum(['git_pull', 'git_clone', 'docker_build', 'npm_build', 'custom']).describe('Loại deployment'),
            environment: z.enum(['development', 'staging', 'production', 'testing']).describe('Môi trường deployment'),
            projectPath: z.string().describe('Đường dẫn dự án trên server'),
            repositoryUrl: z.string().optional().describe('URL repository (cho git_clone)'),
            branch: z.string().optional().describe('Branch để deploy'),
            commitHash: z.string().optional().describe('Commit hash cụ thể'),
            tag: z.string().optional().describe('Tag version'),
            buildScript: z.string().optional().describe('Script build tùy chỉnh'),
            postDeployScript: z.string().optional().describe('Script post-deployment'),
            environmentVariables: z.record(z.string()).optional().describe('Biến môi trường'),
            additionalConfig: z.record(z.any()).optional().describe('Cấu hình bổ sung'),
            customCommands: z.array(z.object({
              command: z.string(),
              description: z.string().optional(),
              workingDirectory: z.string().optional(),
              environment: z.record(z.string()).optional(),
              continueOnError: z.boolean().optional(),
            })).optional().describe('Lệnh tùy chỉnh (cho deployment type custom)'),
          }),
        }
      ),

      // Rollback deployment
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const result = await this.deploymentAutomationTool.rollbackDeployment({
              sshServerId: _args.sshServerId,
              deploymentId: _args.deploymentId,
            }, userId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi rollback deployment: ${error.message}`;
          }
        },
        {
          name: 'rollback_deployment',
          description: 'Rollback deployment về phiên bản trước đó',
          schema: z.object({
            sshServerId: z.number().describe('ID của SSH server'),
            deploymentId: z.number().describe('ID deployment cần rollback'),
          }),
        }
      ),

      // Lấy deployment history
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const userId = parseInt(config?.configurable?.['userId'] || '0');
            const result = await this.deploymentAutomationTool.getDeploymentHistory({
              page: _args.page,
              limit: _args.limit,
              sshServerId: _args.sshServerId,
              projectName: _args.projectName,
              status: _args.status,
              deploymentType: _args.deploymentType,
              environment: _args.environment,
              search: _args.search,
            }, userId);
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi lấy deployment history: ${error.message}`;
          }
        },
        {
          name: 'get_deployment_history',
          description: 'Lấy lịch sử deployment với phân trang và lọc',
          schema: z.object({
            page: z.number().optional().describe('Số trang'),
            limit: z.number().optional().describe('Số items per page'),
            sshServerId: z.number().optional().describe('Lọc theo SSH server ID'),
            projectName: z.string().optional().describe('Lọc theo tên dự án'),
            status: z.string().optional().describe('Lọc theo trạng thái deployment'),
            deploymentType: z.string().optional().describe('Lọc theo loại deployment'),
            environment: z.string().optional().describe('Lọc theo môi trường'),
            search: z.string().optional().describe('Tìm kiếm'),
          }),
        }
      ),

      // Lấy thống kê deployment
      tool(
        async (_args, config: ToolRunnableConfig): Promise<string> => {
          try {
            const result = await this.deploymentAutomationTool.getDeploymentStatistics(
              _args.projectName,
              _args.fromDate,
              _args.toDate,
            );
            return result.success ? 
              `${result.message}\n${JSON.stringify(result.data, null, 2)}` :
              result.message;
          } catch (error) {
            return `Lỗi lấy thống kê deployment: ${error.message}`;
          }
        },
        {
          name: 'get_deployment_statistics',
          description: 'Lấy thống kê deployment theo dự án (tổng số, thành công, thất bại, thời gian trung bình)',
          schema: z.object({
            projectName: z.string().describe('Tên dự án'),
            fromDate: z.number().optional().describe('Từ ngày (timestamp)'),
            toDate: z.number().optional().describe('Đến ngày (timestamp)'),
          }),
        }
      ),
    ];
  }
}
