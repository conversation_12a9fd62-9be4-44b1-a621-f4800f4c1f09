import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO response cho trạng thái deployment
 */
export class DeploymentStatusResponseDto {
  /**
   * ID deployment
   */
  @ApiProperty({
    description: 'ID deployment',
    example: 123,
  })
  deploymentId: number;

  /**
   * Tên dự án
   */
  @ApiProperty({
    description: 'Tên dự án',
    example: 'my-web-app',
  })
  projectName: string;

  /**
   * Loại deployment
   */
  @ApiProperty({
    description: 'Loại deployment',
    enum: ['git_pull', 'git_clone', 'docker_build', 'npm_build', 'custom', 'rollback'],
    example: 'git_pull',
  })
  deploymentType: 'git_pull' | 'git_clone' | 'docker_build' | 'npm_build' | 'custom' | 'rollback';

  /**
   * Môi trường deployment
   */
  @ApiProperty({
    description: 'Môi trường deployment',
    enum: ['development', 'staging', 'production', 'testing'],
    example: 'production',
  })
  environment: 'development' | 'staging' | 'production' | 'testing';

  /**
   * Trạng thái deployment
   */
  @ApiProperty({
    description: 'Trạng thái deployment',
    enum: ['pending', 'running', 'completed', 'failed', 'cancelled', 'rollback'],
    example: 'completed',
  })
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'rollback';

  /**
   * Branch được deploy
   */
  @ApiPropertyOptional({
    description: 'Branch được deploy',
    example: 'main',
  })
  branch?: string;

  /**
   * Commit hash
   */
  @ApiPropertyOptional({
    description: 'Commit hash',
    example: 'abc123def456',
  })
  commitHash?: string;

  /**
   * Tag version
   */
  @ApiPropertyOptional({
    description: 'Tag version',
    example: 'v1.0.0',
  })
  tag?: string;

  /**
   * Thời gian bắt đầu
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu',
    example: 1640995200000,
  })
  startedAt: number;

  /**
   * Thời gian hoàn thành
   */
  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành',
    example: 1640995300000,
  })
  completedAt?: number;

  /**
   * Thời gian thực thi (ms)
   */
  @ApiPropertyOptional({
    description: 'Thời gian thực thi (ms)',
    example: 100000,
  })
  executionTime?: number;

  /**
   * Logs deployment
   */
  @ApiPropertyOptional({
    description: 'Logs deployment',
    example: '=== Command 1: Pull latest code ===\nExit Code: 0\nSTDOUT: Already up to date.',
  })
  logs?: string;

  /**
   * Thông báo lỗi
   */
  @ApiPropertyOptional({
    description: 'Thông báo lỗi',
    example: null,
  })
  errorMessage?: string;

  /**
   * ID job queue
   */
  @ApiPropertyOptional({
    description: 'ID job queue',
    example: 'deployment_job_123',
  })
  jobId?: string;

  /**
   * Metadata
   */
  @ApiPropertyOptional({
    description: 'Metadata',
    example: {
      executionLogIds: [123, 124, 125],
      totalCommands: 3,
      successCount: 3,
      failureCount: 0,
    },
  })
  metadata?: Record<string, any>;

  /**
   * Danh sách execution log IDs
   */
  @ApiProperty({
    description: 'Danh sách execution log IDs',
    example: [123, 124, 125],
  })
  executionLogIds: number[];

  /**
   * ID người deploy
   */
  @ApiProperty({
    description: 'ID người deploy',
    example: 1,
  })
  deployedBy: number;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1640995200000,
  })
  createdAt: number;
}

/**
 * DTO response cho danh sách deployment history với phân trang
 */
export class DeploymentHistoryListResponseDto {
  /**
   * Danh sách deployment history
   */
  @ApiProperty({
    description: 'Danh sách deployment history',
    type: [DeploymentStatusResponseDto],
  })
  items: DeploymentStatusResponseDto[];

  /**
   * Tổng số deployments
   */
  @ApiProperty({
    description: 'Tổng số deployments',
    example: 25,
  })
  total: number;

  /**
   * Trang hiện tại
   */
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  page: number;

  /**
   * Số items per page
   */
  @ApiProperty({
    description: 'Số items per page',
    example: 10,
  })
  limit: number;

  /**
   * Tổng số trang
   */
  @ApiProperty({
    description: 'Tổng số trang',
    example: 3,
  })
  totalPages: number;
}

/**
 * DTO response cho thống kê deployment
 */
export class DeploymentStatisticsResponseDto {
  /**
   * Tổng số deployments
   */
  @ApiProperty({
    description: 'Tổng số deployments',
    example: 100,
  })
  totalDeployments: number;

  /**
   * Số deployments thành công
   */
  @ApiProperty({
    description: 'Số deployments thành công',
    example: 85,
  })
  successfulDeployments: number;

  /**
   * Số deployments thất bại
   */
  @ApiProperty({
    description: 'Số deployments thất bại',
    example: 15,
  })
  failedDeployments: number;

  /**
   * Số deployments đang chạy
   */
  @ApiProperty({
    description: 'Số deployments đang chạy',
    example: 2,
  })
  runningDeployments: number;

  /**
   * Tỷ lệ thành công (%)
   */
  @ApiProperty({
    description: 'Tỷ lệ thành công (%)',
    example: 85,
  })
  successRate: number;

  /**
   * Thời gian deployment trung bình (ms)
   */
  @ApiProperty({
    description: 'Thời gian deployment trung bình (ms)',
    example: 120000,
  })
  averageDeploymentTime: number;

  /**
   * Deployment gần đây nhất
   */
  @ApiPropertyOptional({
    description: 'Deployment gần đây nhất',
    example: {
      id: 123,
      projectName: 'my-web-app',
      status: 'completed',
      deployedAt: 1640995200000,
    },
  })
  lastDeployment?: {
    id: number;
    projectName: string;
    status: string;
    deployedAt: number;
  };
}

/**
 * DTO response cho deployment templates
 */
export class DeploymentTemplateResponseDto {
  /**
   * Tên template
   */
  @ApiProperty({
    description: 'Tên template',
    example: 'Node.js Deployment',
  })
  name: string;

  /**
   * Mô tả template
   */
  @ApiProperty({
    description: 'Mô tả template',
    example: 'Standard Node.js application deployment',
  })
  description: string;

  /**
   * Danh sách lệnh
   */
  @ApiProperty({
    description: 'Danh sách lệnh',
    example: [
      { command: 'git pull', description: 'Pull latest code' },
      { command: 'npm ci', description: 'Install dependencies' },
      { command: 'npm run build', description: 'Build application' },
      { command: 'pm2 restart app', description: 'Restart application' },
    ],
  })
  commands: Array<{
    command: string;
    description: string;
  }>;
}

/**
 * DTO response cho danh sách deployment templates
 */
export class DeploymentTemplateListResponseDto {
  /**
   * Danh sách templates
   */
  @ApiProperty({
    description: 'Danh sách templates',
    type: [DeploymentTemplateResponseDto],
  })
  templates: Record<string, DeploymentTemplateResponseDto>;
}
