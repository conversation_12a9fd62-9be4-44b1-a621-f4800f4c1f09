import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { DeploymentHistory } from '../entities/deployment-history.entity';

/**
 * Repository cho entity DeploymentHistory với tenant isolation
 */
@Injectable()
export class DeploymentHistoryRepository {
  private readonly logger = new Logger(DeploymentHistoryRepository.name);

  constructor(
    @InjectRepository(DeploymentHistory)
    private readonly repository: Repository<DeploymentHistory>,
  ) {}

  /**
   * Tạo lịch sử deployment mới
   * @param data Dữ liệu deployment
   * @returns Deployment history đã tạo
   */
  async create(data: Partial<DeploymentHistory>): Promise<DeploymentHistory> {
    const now = Date.now();
    const deployment = this.repository.create({
      ...data,
      createdAt: now,
      startedAt: data.startedAt || now,
    });
    
    const result = await this.repository.save(deployment);
    this.logger.log(`Đã tạo deployment history mới: ${result.projectName} (ID: ${result.id})`);
    return result;
  }

  /**
   * Tìm deployment history theo ID
   * @param id ID deployment history
   * @returns Deployment history hoặc null
   */
  async findById(id: number): Promise<DeploymentHistory | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm deployment history theo job ID
   * @param jobId ID job queue
   * @returns Deployment history hoặc null
   */
  async findByJobId(jobId: string): Promise<DeploymentHistory | null> {
    return this.repository.findOne({ where: { jobId } });
  }

  /**
   * Lấy danh sách deployment history với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách deployment history và tổng số
   */
  async findWithPagination(options: {
    page?: number;
    limit?: number;
    sshServerId?: number;
    projectName?: string;
    status?: string;
    deploymentType?: string;
    environment?: string;
    deployedBy?: number;
    fromDate?: number;
    toDate?: number;
    search?: string;
  }): Promise<{ items: DeploymentHistory[]; total: number }> {
    const { 
      page = 1, 
      limit = 10, 
      sshServerId, 
      projectName,
      status, 
      deploymentType,
      environment,
      deployedBy,
      fromDate,
      toDate,
      search 
    } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.repository.createQueryBuilder('deployment');

    // Lọc theo server SSH
    if (sshServerId) {
      queryBuilder.andWhere('deployment.sshServerId = :sshServerId', { sshServerId });
    }

    // Lọc theo tên dự án
    if (projectName) {
      queryBuilder.andWhere('deployment.projectName = :projectName', { projectName });
    }

    // Lọc theo trạng thái
    if (status) {
      queryBuilder.andWhere('deployment.status = :status', { status });
    }

    // Lọc theo loại deployment
    if (deploymentType) {
      queryBuilder.andWhere('deployment.deploymentType = :deploymentType', { deploymentType });
    }

    // Lọc theo môi trường
    if (environment) {
      queryBuilder.andWhere('deployment.environment = :environment', { environment });
    }

    // Lọc theo người deploy
    if (deployedBy) {
      queryBuilder.andWhere('deployment.deployedBy = :deployedBy', { deployedBy });
    }

    // Lọc theo khoảng thời gian
    if (fromDate && toDate) {
      queryBuilder.andWhere('deployment.startedAt BETWEEN :fromDate AND :toDate', {
        fromDate,
        toDate,
      });
    } else if (fromDate) {
      queryBuilder.andWhere('deployment.startedAt >= :fromDate', { fromDate });
    } else if (toDate) {
      queryBuilder.andWhere('deployment.startedAt <= :toDate', { toDate });
    }

    // Tìm kiếm trong tên dự án, branch, hoặc commit hash
    if (search) {
      queryBuilder.andWhere(
        '(deployment.projectName ILIKE :search OR deployment.branch ILIKE :search OR deployment.commitHash ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Sắp xếp theo thời gian bắt đầu mới nhất
    queryBuilder.orderBy('deployment.startedAt', 'DESC');

    // Phân trang
    queryBuilder.skip(skip).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return { items, total };
  }

  /**
   * Lấy deployment history gần đây của dự án
   * @param projectName Tên dự án
   * @param limit Số lượng deployment
   * @returns Danh sách deployment history
   */
  async findRecentByProject(projectName: string, limit: number = 10): Promise<DeploymentHistory[]> {
    return this.repository.find({
      where: { projectName },
      order: { startedAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * Lấy deployment thành công cuối cùng của dự án
   * @param projectName Tên dự án
   * @param environment Môi trường
   * @returns Deployment history hoặc null
   */
  async findLastSuccessfulDeployment(
    projectName: string,
    environment?: string,
  ): Promise<DeploymentHistory | null> {
    const where: any = {
      projectName,
      status: 'completed',
    };

    if (environment) {
      where.environment = environment;
    }

    return this.repository.findOne({
      where,
      order: { completedAt: 'DESC' },
    });
  }

  /**
   * Cập nhật trạng thái deployment
   * @param id ID deployment history
   * @param status Trạng thái mới
   * @param logs Logs deployment
   * @param errorMessage Thông báo lỗi
   * @param metadata Metadata bổ sung
   */
  async updateStatus(
    id: number,
    status: DeploymentHistory['status'],
    logs?: string,
    errorMessage?: string,
    metadata?: string,
  ): Promise<void> {
    const updateData: Partial<DeploymentHistory> = {
      status,
      completedAt: ['completed', 'failed', 'cancelled', 'rollback'].includes(status) 
        ? Date.now() 
        : undefined,
    };

    if (logs !== undefined) updateData.logs = logs;
    if (errorMessage !== undefined) updateData.errorMessage = errorMessage;
    if (metadata !== undefined) updateData.metadata = metadata;

    await this.repository.update(id, updateData);
    this.logger.debug(`Cập nhật trạng thái deployment ${id}: ${status}`);
  }

  /**
   * Lấy thống kê deployment theo dự án
   * @param projectName Tên dự án
   * @param fromDate Từ ngày (timestamp)
   * @param toDate Đến ngày (timestamp)
   * @returns Thống kê deployment
   */
  async getStatisticsByProject(
    projectName: string,
    fromDate?: number,
    toDate?: number,
  ): Promise<{
    total: number;
    completed: number;
    failed: number;
    running: number;
    avgDeploymentTime: number;
    successRate: number;
  }> {
    const queryBuilder = this.repository.createQueryBuilder('deployment')
      .where('deployment.projectName = :projectName', { projectName });

    if (fromDate && toDate) {
      queryBuilder.andWhere('deployment.startedAt BETWEEN :fromDate AND :toDate', {
        fromDate,
        toDate,
      });
    }

    const [total, completed, failed, running] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('deployment.status = :status', { status: 'completed' }).getCount(),
      queryBuilder.clone().andWhere('deployment.status = :status', { status: 'failed' }).getCount(),
      queryBuilder.clone().andWhere('deployment.status = :status', { status: 'running' }).getCount(),
    ]);

    // Tính thời gian deployment trung bình
    const avgResult = await queryBuilder.clone()
      .select('AVG(deployment.completedAt - deployment.startedAt)', 'avg')
      .andWhere('deployment.completedAt IS NOT NULL')
      .andWhere('deployment.startedAt IS NOT NULL')
      .getRawOne();

    const avgDeploymentTime = Math.round(avgResult?.avg || 0);
    const successRate = total > 0 ? Math.round((completed / total) * 100) : 0;

    return {
      total,
      completed,
      failed,
      running,
      avgDeploymentTime,
      successRate,
    };
  }

  /**
   * Lấy deployment đang chạy
   * @returns Danh sách deployment đang chạy
   */
  async findRunningDeployments(): Promise<DeploymentHistory[]> {
    return this.repository.find({
      where: { status: 'running' },
      order: { startedAt: 'ASC' },
    });
  }

  /**
   * Xóa deployment history cũ
   * @param olderThanDays Xóa deployment cũ hơn số ngày
   * @returns Số lượng deployment đã xóa
   */
  async deleteOldDeployments(olderThanDays: number): Promise<number> {
    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    const result = await this.repository.delete({
      startedAt: Between(0, cutoffDate),
      status: 'completed', // Chỉ xóa deployment đã hoàn thành
    });

    const deletedCount = result.affected || 0;
    this.logger.log(`Đã xóa ${deletedCount} deployment histories cũ hơn ${olderThanDays} ngày`);
    
    return deletedCount;
  }
}
