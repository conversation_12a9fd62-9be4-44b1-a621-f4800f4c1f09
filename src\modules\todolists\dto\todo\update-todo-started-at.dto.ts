import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional } from 'class-validator';

/**
 * DTO cho cập nhật thời gian bắt đầu làm việc
 */
export class UpdateTodoStartedAtDto {
  /**
   * Thời gian bắt đầu làm việc (timestamp trong milliseconds)
   * Nếu null hoặc không truyền, sẽ set thành thời gian hiện tại
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian bắt đầu làm việc (timestamp trong milliseconds). N<PERSON><PERSON> không truy<PERSON>n, sẽ dùng thời gian hiện tại',
    example: 1625097600000,
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsInt({ message: 'Thời gian bắt đầu phải là số nguyên (timestamp)' })
  startedAt?: number | null;
}
