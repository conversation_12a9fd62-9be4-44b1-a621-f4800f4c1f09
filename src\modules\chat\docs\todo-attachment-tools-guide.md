# Todo Attachment Tools Guide - Multi-Agent System

## 📋 Tổng Quan

TodoAttachmentToolsProvider cung cấp các tools để quản lý tệp đính kèm công việc trong multi-agent system. Đ<PERSON> được cập nhật với tool mới để lấy **TẤT CẢ** danh sách tệp đính kèm không phân trang.

## 🛠️ Danh Sách Tools

### 1. **create_attachment_upload_url**
T<PERSON><PERSON> presigned URL để upload tệp đính kèm.

**Parameters:**
- `fileName` (string): Tên tệp
- `fileSize` (number): Kích thước tệp (bytes)
- `mimeType` (string): Loại MIME của tệp
- `todoId` (number): ID công việc

**Example:**
```json
{
  "fileName": "document.pdf",
  "fileSize": 1024000,
  "mimeType": "application/pdf",
  "todoId": 150
}
```

### 2. **confirm_attachment_upload**
<PERSON><PERSON><PERSON> nhận upload thành công và lưu thông tin tệp đính kèm.

**Parameters:**
- `s3Key` (string): Key của file trên S3/Cloud Storage
- `todoId` (number): ID công việc
- `fileName` (string): Tên tệp
- `size` (number, optional): Kích thước tệp thực tế
- `contentType` (string, optional): Loại MIME của tệp

### 3. **get_todo_attachments**
Lấy danh sách tệp đính kèm của một công việc cụ thể.

**Parameters:**
- `todoId` (number): ID công việc

**Example:**
```json
{
  "todoId": 150
}
```

### 4. **get_all_attachments_paginated**
Lấy danh sách tệp đính kèm với phân trang và bộ lọc.

**Parameters:**
- `page` (number, optional): Số trang (default: 1)
- `limit` (number, optional): Số lượng kết quả trên mỗi trang (default: 20)
- `todoId` (number, optional): Lọc theo ID công việc
- `createdBy` (number, optional): Lọc theo ID người tạo
- `search` (string, optional): Tìm kiếm theo tên tệp
- `sortBy` (string, optional): Trường sắp xếp (default: 'createdAt')
- `sortDirection` (enum: 'ASC'|'DESC', optional): Hướng sắp xếp (default: 'DESC')

### 5. **get_all_attachments_no_pagination** ⭐ **MỚI**
Lấy **TẤT CẢ** danh sách tệp đính kèm không phân trang - hữu ích cho export, dropdown, thống kê tổng quan.

**Parameters:**
- `todoId` (number, optional): Lọc theo ID công việc
- `createdBy` (number, optional): Lọc theo ID người tạo
- `search` (string, optional): Tìm kiếm theo tên tệp
- `sortBy` (string, optional): Trường sắp xếp (createdAt, filename, size) (default: 'createdAt')
- `sortDirection` (enum: 'ASC'|'DESC', optional): Hướng sắp xếp (default: 'DESC')

**Use Cases:**
- **Export dữ liệu**: Lấy tất cả để xuất Excel/PDF
- **Dropdown/Select**: Hiển thị danh sách không cần phân trang
- **Dashboard**: Thống kê tổng quan
- **Bulk operations**: Xử lý hàng loạt

**Examples:**
```json
// Lấy tất cả tệp đính kèm
{}

// Lấy tất cả tệp đính kèm của một công việc
{
  "todoId": 150
}

// Lấy tất cả tệp PDF, sắp xếp theo tên
{
  "search": "pdf",
  "sortBy": "filename",
  "sortDirection": "ASC"
}

// Lấy tất cả tệp của một người tạo
{
  "createdBy": 1,
  "sortBy": "createdAt",
  "sortDirection": "DESC"
}
```

### 6. **get_attachment_details**
Lấy chi tiết tệp đính kèm theo ID.

**Parameters:**
- `attachmentId` (number): ID tệp đính kèm

### 7. **delete_attachment**
Xóa tệp đính kèm.

**Parameters:**
- `attachmentId` (number): ID tệp đính kèm

### 8. **add_attachment_legacy**
Thêm tệp đính kèm cho công việc (phương pháp cũ).

**Parameters:**
- `todoId` (number): ID công việc
- `fileName` (string): Tên tệp
- `fileUrl` (string): URL tệp
- `fileSize` (number): Kích thước tệp (bytes)
- `mimeType` (string): Loại MIME của tệp

## 🔧 Cách Sử Dụng Trong Multi-Agent

### Scenario 1: Export Tất Cả Tệp Đính Kèm
```javascript
// Agent có thể sử dụng tool này để lấy tất cả tệp đính kèm cho export
const result = await tools.get_all_attachments_no_pagination({
  sortBy: "createdAt",
  sortDirection: "DESC"
});
```

### Scenario 2: Thống Kê Tệp Đính Kèm Theo Công Việc
```javascript
// Lấy tất cả tệp đính kèm của một công việc để thống kê
const result = await tools.get_all_attachments_no_pagination({
  todoId: 150
});
```

### Scenario 3: Tìm Kiếm Tệp Theo Loại
```javascript
// Tìm tất cả tệp PDF trong hệ thống
const result = await tools.get_all_attachments_no_pagination({
  search: "pdf",
  sortBy: "filename",
  sortDirection: "ASC"
});
```

## 📊 So Sánh Tools

| Tool | Phân Trang | Use Case | Response Type |
|------|------------|----------|---------------|
| `get_all_attachments_paginated` | ✅ Có | Hiển thị danh sách UI | PaginatedResult |
| `get_all_attachments_no_pagination` | ❌ Không | Export, thống kê, dropdown | Array |
| `get_todo_attachments` | ❌ Không | Tệp của 1 công việc | Array |

## 🚀 Lợi Ích Tool Mới

1. **Performance**: Không cần gọi nhiều lần API với phân trang
2. **Simplicity**: Trả về array đơn giản, dễ xử lý
3. **Flexibility**: Hỗ trợ đầy đủ bộ lọc và sắp xếp
4. **Multi-purpose**: Phù hợp cho nhiều use case khác nhau

## ⚠️ Lưu Ý

- Tool mới không có giới hạn số lượng, cần cẩn thận với dữ liệu lớn
- Luôn sử dụng bộ lọc phù hợp để tối ưu performance
- Tenant isolation được đảm bảo tự động thông qua tenantId trong config
