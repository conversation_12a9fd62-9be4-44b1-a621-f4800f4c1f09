import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as request from 'supertest';
import { SshServerModule } from '../ssh-server.module';
import { SshServer } from '../entities/ssh-server.entity';
import { SshExecutionLog } from '../entities/ssh-execution-log.entity';
import { DeploymentHistory } from '../entities/deployment-history.entity';
import { AuthModule } from '@/modules/auth/auth.module';
import { ServicesModule } from '@/shared/services/services.module';

describe('SSH Execution Integration Tests', () => {
  let app: INestApplication;
  let authToken: string;
  let sshServerId: number;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [SshServer, SshExecutionLog, DeploymentHistory],
          synchronize: true,
          logging: false,
        }),
        SshServerModule,
        AuthModule,
        ServicesModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    await app.close();
  });

  async function setupTestData() {
    // Create test user and get auth token
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.data.accessToken;

    // Create test SSH server
    const createServerResponse = await request(app.getHttpServer())
      .post('/ssh-servers')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        name: 'Test Server',
        host: 'localhost',
        port: 22,
        username: 'testuser',
        password: 'testpassword',
        description: 'Test SSH server for integration tests',
      });

    sshServerId = createServerResponse.body.data.id;
  }

  describe('SSH Server Management', () => {
    it('should create SSH server', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-servers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'New Test Server',
          host: '*************',
          port: 22,
          username: 'ubuntu',
          password: 'secure-password',
          description: 'New test server',
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('New Test Server');
      expect(response.body.data.host).toBe('*************');
    });

    it('should get SSH servers list', async () => {
      const response = await request(app.getHttpServer())
        .get('/ssh-servers')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toBeInstanceOf(Array);
      expect(response.body.data.total).toBeGreaterThan(0);
    });

    it('should get SSH server by ID', async () => {
      const response = await request(app.getHttpServer())
        .get(`/ssh-servers/${sshServerId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(sshServerId);
      expect(response.body.data.name).toBe('Test Server');
    });

    it('should update SSH server', async () => {
      const response = await request(app.getHttpServer())
        .put(`/ssh-servers/${sshServerId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          description: 'Updated test server description',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.description).toBe('Updated test server description');
    });

    it('should get SSH server statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/ssh-servers/statistics/overview')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('active');
      expect(response.body.data).toHaveProperty('inactive');
    });
  });

  describe('SSH Command Execution', () => {
    it('should execute single SSH command', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/command')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId,
          command: 'echo "Hello World"',
          description: 'Test echo command',
        })
        .expect(200);

      expect(response.body.data).toHaveProperty('logId');
      expect(response.body.data).toHaveProperty('command');
      expect(response.body.data).toHaveProperty('stdout');
      expect(response.body.data).toHaveProperty('exitCode');
    });

    it('should execute batch SSH commands', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/batch')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId,
          commands: [
            {
              command: 'echo "Command 1"',
              description: 'First test command',
            },
            {
              command: 'echo "Command 2"',
              description: 'Second test command',
            },
          ],
          description: 'Test batch commands',
        })
        .expect(200);

      expect(response.body.data).toHaveProperty('logIds');
      expect(response.body.data).toHaveProperty('totalCommands');
      expect(response.body.data).toHaveProperty('results');
      expect(response.body.data.totalCommands).toBe(2);
    });

    it('should get execution logs', async () => {
      const response = await request(app.getHttpServer())
        .get('/ssh-execution/logs')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          page: 1,
          limit: 10,
          sshServerId,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toBeInstanceOf(Array);
      expect(response.body.data).toHaveProperty('total');
      expect(response.body.data).toHaveProperty('page');
      expect(response.body.data).toHaveProperty('limit');
    });

    it('should get deployment templates', async () => {
      const response = await request(app.getHttpServer())
        .get('/ssh-execution/deployment-templates')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.templates).toHaveProperty('NODEJS');
      expect(response.body.data.templates).toHaveProperty('REACT');
      expect(response.body.data.templates).toHaveProperty('DOCKER');
    });
  });

  describe('Deployment Operations', () => {
    it('should deploy project with git_pull', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/deploy')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId,
          projectName: 'test-project',
          deploymentType: 'git_pull',
          environment: 'development',
          projectPath: '/tmp/test-project',
          branch: 'main',
          buildScript: 'echo "Building..."',
        })
        .expect(200);

      expect(response.body.data).toHaveProperty('deploymentId');
      expect(response.body.data).toHaveProperty('projectName');
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data.projectName).toBe('test-project');
    });

    it('should deploy with template', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/deploy-template')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId,
          templateName: 'NODEJS',
          projectName: 'nodejs-test-project',
          projectPath: '/tmp/nodejs-project',
        })
        .expect(200);

      expect(response.body.data).toHaveProperty('logIds');
      expect(response.body.data).toHaveProperty('totalCommands');
      expect(response.body.data).toHaveProperty('results');
    });

    it('should get deployment history', async () => {
      const response = await request(app.getHttpServer())
        .get('/ssh-execution/logs')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          executionType: 'deployment',
          sshServerId,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.items).toBeInstanceOf(Array);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid SSH server ID', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/command')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId: 99999,
          command: 'echo "test"',
        })
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    it('should handle invalid command', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/command')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId,
          command: '', // Empty command
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should handle unauthorized access', async () => {
      await request(app.getHttpServer())
        .get('/ssh-servers')
        .expect(401);
    });

    it('should handle invalid deployment config', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/deploy')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId,
          projectName: '', // Empty project name
          deploymentType: 'git_pull',
          environment: 'development',
          projectPath: '/tmp/test',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('Validation', () => {
    it('should validate SSH server creation data', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-servers')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: '', // Empty name
          host: 'invalid-host',
          port: 'invalid-port', // Invalid port type
          username: '',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should validate command execution data', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/command')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId: 'invalid-id', // Invalid ID type
          command: null, // Null command
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should validate deployment data', async () => {
      const response = await request(app.getHttpServer())
        .post('/ssh-execution/deploy')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sshServerId,
          projectName: 'test',
          deploymentType: 'invalid-type', // Invalid deployment type
          environment: 'invalid-env', // Invalid environment
          projectPath: '',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});
