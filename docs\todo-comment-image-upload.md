# Hướng Dẫn Upload Ảnh Cho Bình Luận Todo

## 📋 Tổng Quan

Tính năng upload ảnh cho bình luận todo cho phép người dùng đính kèm ảnh vào bình luận của họ. Luồng hoạt động:

1. **Frontend** gọi API lấy URL tạm thời để upload ảnh
2. **Frontend** upload ảnh trực tiếp lên cloud storage qua URL tạm thời
3. **Frontend** gọi API xác nhận upload thành công
4. **Frontend** tạo bình luận với link ảnh đã upload

## 🔄 Luồng Hoạt Động Chi Tiết

### Bước 1: Lấy URL Upload Tạm Thời

**Endpoint:** `POST /api/todo-comments/image-upload-url`

**Request:**
```json
{
  "todoId": 123,
  "fileName": "comment-image.jpg",
  "mimeType": "image/jpeg",
  "fileSize": 1024000,
  "description": "Ảnh minh họa cho bình luận"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "uploadUrl": "https://storage.googleapis.com/bucket/upload?path=todo-comments%2F123%2F1625097600000_abc123.jpg&uploadId=upload_123_456_1625097600000",
    "publicUrl": "https://storage.googleapis.com/bucket/todo-comments/123/1625097600000_abc123.jpg",
    "expiresAt": 1625098500000,
    "uploadId": "upload_123_456_1625097600000",
    "metadata": {
      "maxFileSize": 10485760,
      "allowedMimeTypes": ["image/jpeg", "image/png", "image/gif", "image/webp"]
    }
  }
}
```

### Bước 2: Upload Ảnh Lên Cloud Storage

**Frontend** sử dụng `uploadUrl` để upload file trực tiếp:

```javascript
// Ví dụ với fetch API
const formData = new FormData();
formData.append('file', imageFile);

const uploadResponse = await fetch(uploadUrl, {
  method: 'PUT', // hoặc POST tùy theo cloud provider
  body: formData
});

if (uploadResponse.ok) {
  // Upload thành công, chuyển sang bước 3
}
```

### Bước 3: Xác Nhận Upload Thành Công

**Endpoint:** `POST /api/todo-comments/confirm-image-upload`

**Request:**
```json
{
  "uploadId": "upload_123_456_1625097600000",
  "todoId": 123,
  "imageUrl": "https://storage.googleapis.com/bucket/todo-comments/123/1625097600000_abc123.jpg",
  "fileName": "comment-image.jpg",
  "fileSize": 1024000,
  "mimeType": "image/jpeg",
  "description": "Ảnh minh họa cho bình luận"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "imageId": "img_123_456_1625097600000",
    "imageUrl": "https://storage.googleapis.com/bucket/todo-comments/123/1625097600000_abc123.jpg",
    "thumbnailUrl": "https://storage.googleapis.com/bucket/todo-comments/123/1625097600000_abc123_thumb.jpg",
    "fileName": "comment-image.jpg",
    "fileSize": 1024000,
    "mimeType": "image/jpeg",
    "uploadedAt": 1625097600000
  }
}
```

### Bước 4: Tạo Bình Luận Với Ảnh

**Endpoint:** `POST /api/todo-comments`

**Request:**
```json
{
  "todoId": 123,
  "content": "Đây là bình luận có ảnh đính kèm",
  "resources": [
    {
      "type": "image",
      "url": "https://storage.googleapis.com/bucket/todo-comments/123/1625097600000_abc123.jpg",
      "name": "comment-image.jpg",
      "size": 1024000,
      "mimeType": "image/jpeg"
    }
  ]
}
```

## 🔧 Cấu Hình & Giới Hạn

### Loại File Được Hỗ Trợ
- **JPEG** (image/jpeg)
- **PNG** (image/png)
- **GIF** (image/gif)
- **WebP** (image/webp)

### Giới Hạn Kích Thước
- **Tối đa:** 10MB (10,485,760 bytes)
- **Khuyến nghị:** Dưới 5MB để tối ưu tốc độ

### Thời Gian Hết Hạn
- **Upload URL:** 15 phút
- **Cần hoàn thành upload trong thời gian này**

## 🛡️ Bảo Mật & Quyền Hạn

### Kiểm Tra Quyền
- Chỉ **người tạo công việc** hoặc **người được giao việc** mới có thể upload ảnh
- Upload ID được validate để đảm bảo tính bảo mật
- File type và size được validate nghiêm ngặt

### Validation
- **Tên file:** Tối đa 255 ký tự
- **MIME type:** Phải thuộc danh sách được phép
- **Kích thước:** Không vượt quá giới hạn
- **Upload ID:** Phải đúng format và thuộc về user hiện tại

## 💻 Ví Dụ Frontend Implementation

### React Hook cho Upload Ảnh

```typescript
import { useState } from 'react';

interface UseImageUploadResult {
  uploadImage: (file: File, todoId: number) => Promise<string>;
  isUploading: boolean;
  error: string | null;
}

export const useImageUpload = (): UseImageUploadResult => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadImage = async (file: File, todoId: number): Promise<string> => {
    setIsUploading(true);
    setError(null);

    try {
      // Bước 1: Lấy upload URL
      const uploadUrlResponse = await fetch('/api/todo-comments/image-upload-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          todoId,
          fileName: file.name,
          mimeType: file.type,
          fileSize: file.size
        })
      });

      const uploadUrlData = await uploadUrlResponse.json();
      
      if (!uploadUrlData.success) {
        throw new Error(uploadUrlData.message);
      }

      // Bước 2: Upload file
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await fetch(uploadUrlData.data.uploadUrl, {
        method: 'PUT',
        body: formData
      });

      if (!uploadResponse.ok) {
        throw new Error('Upload failed');
      }

      // Bước 3: Xác nhận upload
      const confirmResponse = await fetch('/api/todo-comments/confirm-image-upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          uploadId: uploadUrlData.data.uploadId,
          todoId,
          imageUrl: uploadUrlData.data.publicUrl,
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type
        })
      });

      const confirmData = await confirmResponse.json();
      
      if (!confirmData.success) {
        throw new Error(confirmData.message);
      }

      return confirmData.data.imageUrl;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      throw err;
    } finally {
      setIsUploading(false);
    }
  };

  return { uploadImage, isUploading, error };
};
```

### Component Upload Ảnh

```tsx
import React, { useRef } from 'react';
import { useImageUpload } from './useImageUpload';

interface ImageUploadProps {
  todoId: number;
  onImageUploaded: (imageUrl: string) => void;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({ todoId, onImageUploaded }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadImage, isUploading, error } = useImageUpload();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const imageUrl = await uploadImage(file, todoId);
      onImageUploaded(imageUrl);
    } catch (err) {
      console.error('Upload failed:', err);
    }
  };

  return (
    <div>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/gif,image/webp"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
      
      <button
        onClick={() => fileInputRef.current?.click()}
        disabled={isUploading}
      >
        {isUploading ? 'Đang upload...' : 'Chọn ảnh'}
      </button>
      
      {error && <div style={{ color: 'red' }}>{error}</div>}
    </div>
  );
};
```

## 🚨 Error Handling

### Các Lỗi Thường Gặp

| Error Code | Mô Tả | Giải Pháp |
|------------|-------|-----------|
| 13048 | Upload ảnh bình luận thất bại | Kiểm tra kết nối mạng và thử lại |
| 13049 | Loại file không được hỗ trợ | Chỉ sử dụng JPEG, PNG, GIF, WebP |
| 13050 | Kích thước file quá lớn | Giảm kích thước file xuống dưới 10MB |
| 13051 | Upload ID không hợp lệ | Lấy upload URL mới và thử lại |

### Best Practices

1. **Validate file trước khi upload**
2. **Hiển thị progress bar cho user experience**
3. **Implement retry mechanism cho network errors**
4. **Compress ảnh trước khi upload nếu cần**
5. **Cache upload URLs để tránh gọi API không cần thiết**

## 🔄 Tích Hợp Với Cloud Storage

### Google Cloud Storage
```javascript
// Cấu hình upload cho GCS
const uploadToGCS = async (uploadUrl, file) => {
  return fetch(uploadUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type
    }
  });
};
```

### AWS S3
```javascript
// Cấu hình upload cho S3
const uploadToS3 = async (uploadUrl, file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return fetch(uploadUrl, {
    method: 'POST',
    body: formData
  });
};
```

Tính năng upload ảnh bình luận đã sẵn sàng sử dụng! 🎯
