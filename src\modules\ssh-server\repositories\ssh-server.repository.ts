import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { SshServer } from '../entities/ssh-server.entity';

/**
 * Repository cho entity SshServer với tenant isolation
 */
@Injectable()
export class SshServerRepository {
  private readonly logger = new Logger(SshServerRepository.name);

  constructor(
    @InjectRepository(SshServer)
    private readonly repository: Repository<SshServer>,
  ) {}

  /**
   * Tạo server SSH mới
   * @param data Dữ liệu server SSH
   * @returns Server SSH đã tạo
   */
  async create(data: Partial<SshServer>): Promise<SshServer> {
    const now = Date.now();
    const server = this.repository.create({
      ...data,
      createdAt: now,
      updatedAt: now,
    });
    
    const result = await this.repository.save(server);
    this.logger.log(`Đã tạo SSH server mới: ${result.name} (ID: ${result.id})`);
    return result;
  }

  /**
   * Tìm server SSH theo ID
   * @param id ID server SSH
   * @returns Server SSH hoặc null
   */
  async findById(id: number): Promise<SshServer | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm server SSH theo tên trong tenant
   * @param name Tên server SSH
   * @returns Server SSH hoặc null
   */
  async findByName(name: string): Promise<SshServer | null> {
    return this.repository.findOne({ where: { name } });
  }

  /**
   * Lấy danh sách server SSH với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách server SSH và tổng số
   */
  async findWithPagination(options: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<{ items: SshServer[]; total: number }> {
    const { page = 1, limit = 10, search, isActive } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.repository.createQueryBuilder('ssh_server');

    // Tìm kiếm theo tên hoặc host
    if (search) {
      queryBuilder.andWhere(
        '(ssh_server.name ILIKE :search OR ssh_server.host ILIKE :search OR ssh_server.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Lọc theo trạng thái
    if (typeof isActive === 'boolean') {
      queryBuilder.andWhere('ssh_server.isActive = :isActive', { isActive });
    }

    // Sắp xếp theo thời gian tạo mới nhất
    queryBuilder.orderBy('ssh_server.createdAt', 'DESC');

    // Phân trang
    queryBuilder.skip(skip).take(limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    return { items, total };
  }

  /**
   * Lấy tất cả server SSH đang hoạt động
   * @returns Danh sách server SSH đang hoạt động
   */
  async findAllActive(): Promise<SshServer[]> {
    return this.repository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Cập nhật server SSH
   * @param id ID server SSH
   * @param data Dữ liệu cập nhật
   * @returns Server SSH đã cập nhật
   */
  async update(id: number, data: Partial<SshServer>): Promise<SshServer> {
    const updateData = {
      ...data,
      updatedAt: Date.now(),
    };

    await this.repository.update(id, updateData);
    const updated = await this.findById(id);
    
    if (updated) {
      this.logger.log(`Đã cập nhật SSH server: ${updated.name} (ID: ${id})`);
    }
    
    return updated!;
  }

  /**
   * Cập nhật trạng thái kết nối cuối cùng
   * @param id ID server SSH
   * @param status Trạng thái kết nối
   * @param error Lỗi kết nối (nếu có)
   */
  async updateConnectionStatus(
    id: number, 
    status: string, 
    error?: string
  ): Promise<void> {
    const updateData: Partial<SshServer> = {
      lastConnectionStatus: status,
      lastConnectedAt: status === 'connected' ? Date.now() : undefined,
      lastConnectionError: error || null,
      updatedAt: Date.now(),
    };

    await this.repository.update(id, updateData);
    this.logger.debug(`Cập nhật trạng thái kết nối SSH server ${id}: ${status}`);
  }

  /**
   * Xóa mềm server SSH
   * @param id ID server SSH
   */
  async softDelete(id: number): Promise<void> {
    await this.repository.update(id, { 
      isActive: false,
      updatedAt: Date.now(),
    });
    this.logger.log(`Đã xóa mềm SSH server ID: ${id}`);
  }

  /**
   * Xóa cứng server SSH
   * @param id ID server SSH
   */
  async hardDelete(id: number): Promise<void> {
    await this.repository.delete(id);
    this.logger.log(`Đã xóa cứng SSH server ID: ${id}`);
  }

  /**
   * Kiểm tra tên server SSH đã tồn tại
   * @param name Tên server SSH
   * @param excludeId ID server SSH cần loại trừ (cho trường hợp update)
   * @returns True nếu tên đã tồn tại
   */
  async isNameExists(name: string, excludeId?: number): Promise<boolean> {
    const where: FindOptionsWhere<SshServer> = { name };
    
    if (excludeId) {
      where.id = { $ne: excludeId } as any;
    }

    const count = await this.repository.count({ where });
    return count > 0;
  }

  /**
   * Lấy thống kê server SSH
   * @returns Thống kê server SSH
   */
  async getStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    connected: number;
    disconnected: number;
  }> {
    const [total, active, connected] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { isActive: true } }),
      this.repository.count({ 
        where: { 
          isActive: true, 
          lastConnectionStatus: 'connected' 
        } 
      }),
    ]);

    return {
      total,
      active,
      inactive: total - active,
      connected,
      disconnected: active - connected,
    };
  }
}
