import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt, Min, IsOptional, MaxLength, IsUrl } from 'class-validator';

/**
 * DTO cho xác nhận upload ảnh bình luận thành công
 */
export class ConfirmCommentImageUploadDto {
  /**
   * ID tạm thời từ response của API lấy upload URL
   * @example "upload_123_456_789"
   */
  @ApiProperty({
    description: 'ID tạm thời từ response của API lấy upload URL',
    example: 'upload_123_456_789',
    required: true,
  })
  @IsNotEmpty({ message: 'Upload ID không được để trống' })
  @IsString({ message: 'Upload ID phải là chuỗi' })
  @MaxLength(100, { message: 'Upload ID không được vượt quá 100 ký tự' })
  uploadId: string;

  /**
   * ID của công việc mà bình luận thuộc về
   * @example 123
   */
  @ApiProperty({
    description: 'ID của công việc mà bình luận thuộc về',
    example: 123,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * URL công khai của ảnh đã upload thành công
   * @example "https://storage.googleapis.com/bucket/todo-comments/123/image.jpg"
   */
  @ApiProperty({
    description: 'URL công khai của ảnh đã upload thành công',
    example: 'https://storage.googleapis.com/bucket/todo-comments/123/image.jpg',
    required: true,
  })
  @IsNotEmpty({ message: 'URL ảnh không được để trống' })
  @IsString({ message: 'URL ảnh phải là chuỗi' })
  @IsUrl({}, { message: 'URL ảnh không hợp lệ' })
  @MaxLength(500, { message: 'URL ảnh không được vượt quá 500 ký tự' })
  imageUrl: string;

  /**
   * Tên file ảnh gốc
   * @example "comment-image.jpg"
   */
  @ApiProperty({
    description: 'Tên file ảnh gốc',
    example: 'comment-image.jpg',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString({ message: 'Tên file phải là chuỗi' })
  @MaxLength(255, { message: 'Tên file không được vượt quá 255 ký tự' })
  fileName: string;

  /**
   * Kích thước file thực tế đã upload (bytes)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước file thực tế đã upload (bytes)',
    example: 1024000,
    required: true,
  })
  @IsNotEmpty({ message: 'Kích thước file không được để trống' })
  @IsInt({ message: 'Kích thước file phải là số nguyên' })
  @Min(1, { message: 'Kích thước file phải lớn hơn 0' })
  fileSize: number;

  /**
   * Loại MIME của file ảnh
   * @example "image/jpeg"
   */
  @ApiProperty({
    description: 'Loại MIME của file ảnh',
    example: 'image/jpeg',
    required: true,
  })
  @IsNotEmpty({ message: 'Loại MIME không được để trống' })
  @IsString({ message: 'Loại MIME phải là chuỗi' })
  @MaxLength(100, { message: 'Loại MIME không được vượt quá 100 ký tự' })
  mimeType: string;

  /**
   * Mô tả ảnh (tùy chọn)
   * @example "Ảnh minh họa cho bình luận"
   */
  @ApiProperty({
    description: 'Mô tả ảnh (tùy chọn)',
    example: 'Ảnh minh họa cho bình luận',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  @MaxLength(500, { message: 'Mô tả không được vượt quá 500 ký tự' })
  description?: string;
}

/**
 * DTO cho response xác nhận upload ảnh bình luận
 */
export class ConfirmCommentImageUploadResponseDto {
  /**
   * ID của ảnh đã được lưu trong hệ thống
   * @example "img_123_456"
   */
  @ApiProperty({
    description: 'ID của ảnh đã được lưu trong hệ thống',
    example: 'img_123_456',
  })
  imageId: string;

  /**
   * URL công khai của ảnh
   * @example "https://storage.googleapis.com/bucket/todo-comments/123/image.jpg"
   */
  @ApiProperty({
    description: 'URL công khai của ảnh',
    example: 'https://storage.googleapis.com/bucket/todo-comments/123/image.jpg',
  })
  imageUrl: string;

  /**
   * URL thumbnail của ảnh (nếu có)
   * @example "https://storage.googleapis.com/bucket/todo-comments/123/thumb_image.jpg"
   */
  @ApiProperty({
    description: 'URL thumbnail của ảnh (nếu có)',
    example: 'https://storage.googleapis.com/bucket/todo-comments/123/thumb_image.jpg',
    nullable: true,
  })
  thumbnailUrl?: string;

  /**
   * Tên file ảnh
   * @example "comment-image.jpg"
   */
  @ApiProperty({
    description: 'Tên file ảnh',
    example: 'comment-image.jpg',
  })
  fileName: string;

  /**
   * Kích thước file (bytes)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1024000,
  })
  fileSize: number;

  /**
   * Loại MIME
   * @example "image/jpeg"
   */
  @ApiProperty({
    description: 'Loại MIME',
    example: 'image/jpeg',
  })
  mimeType: string;

  /**
   * Thời gian upload thành công (timestamp)
   * @example 1625097600000
   */
  @ApiProperty({
    description: 'Thời gian upload thành công (timestamp)',
    example: 1625097600000,
  })
  uploadedAt: number;
}
