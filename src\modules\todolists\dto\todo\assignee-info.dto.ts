import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin người được giao công việc
 */
export class AssigneeInfoDto {
  /**
   * ID của người được giao công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người được giao công việc',
    example: 1,
  })
  id: number;

  /**
   * Tên của người được giao công việc
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của người được giao công việc',
    example: 'Nguyễn Văn <PERSON>',
  })
  name: string;

  /**
   * Email của người được giao công việc
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của người được giao công việc',
    example: '<EMAIL>',
    nullable: true,
  })
  email?: string | null;

  /**
   * Mã nhân viên
   * @example "REDAI001"
   */
  @ApiProperty({
    description: 'Mã nhân viên',
    example: 'REDAI001',
    nullable: true,
  })
  employeeCode?: string | null;
}
