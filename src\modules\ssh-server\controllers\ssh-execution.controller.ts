import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/dto/api-response.dto';
import { SshCommandService } from '../services/ssh-command.service';
import { DeploymentService } from '../services/deployment.service';
import { SshExecutionLogRepository } from '../repositories/ssh-execution-log.repository';
import { DeploymentHistoryRepository } from '../repositories/deployment-history.repository';
import {
  ExecuteCommandDto,
  ExecuteBatchCommandsDto,
} from '../dtos/requests/execute-command.dto';
import {
  DeployProjectDto,
  RollbackDeploymentDto,
  DeploymentTemplateDto,
} from '../dtos/requests/deploy-project.dto';
import {
  CommandResultResponseDto,
  BatchCommandResultResponseDto,
  ExecutionLogResponseDto,
  ExecutionLogListResponseDto,
} from '../dtos/responses/command-result.dto';
import {
  DeploymentStatusResponseDto,
  DeploymentHistoryListResponseDto,
  DeploymentStatisticsResponseDto,
  DeploymentTemplateResponseDto,
  DeploymentTemplateListResponseDto,
} from '../dtos/responses/deployment-status.dto';

/**
 * Controller thực thi lệnh SSH và deployment
 */
@ApiTags('SSH Command Execution')
@Controller('ssh-execution')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class SshExecutionController {
  constructor(
    private readonly sshCommandService: SshCommandService,
    private readonly deploymentService: DeploymentService,
    private readonly sshExecutionLogRepository: SshExecutionLogRepository,
    private readonly deploymentHistoryRepository: DeploymentHistoryRepository,
  ) {}

  /**
   * Thực thi lệnh SSH đơn lẻ
   */
  @Post('command')
  @ApiOperation({
    summary: 'Thực thi lệnh SSH đơn lẻ',
    description: 'Thực thi một lệnh SSH trên server được chỉ định',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả thực thi lệnh',
    type: ApiResponseDto<CommandResultResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async executeCommand(
    @Body() executeDto: ExecuteCommandDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CommandResultResponseDto>> {
    const { result, logId } = await this.sshCommandService.executeCommand(
      executeDto.sshServerId,
      executeDto.command,
      user.sub,
      {
        workingDirectory: executeDto.workingDirectory,
        environment: executeDto.environment,
        timeout: executeDto.timeout,
        executionType: executeDto.executionType,
        metadata: executeDto.metadata,
      },
    );

    const responseData: CommandResultResponseDto = {
      logId,
      command: result.command,
      stdout: result.stdout,
      stderr: result.stderr,
      exitCode: result.exitCode,
      executionTime: result.executionTime,
      success: result.success,
      error: result.error,
      executedAt: Date.now(),
    };

    return {
      success: result.success,
      message: result.success ? 'Lệnh thực thi thành công' : 'Lệnh thực thi thất bại',
      data: responseData,
    };
  }

  /**
   * Thực thi batch commands
   */
  @Post('batch')
  @ApiOperation({
    summary: 'Thực thi batch commands',
    description: 'Thực thi nhiều lệnh SSH theo thứ tự',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả thực thi batch commands',
    type: ApiResponseDto<BatchCommandResultResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async executeBatchCommands(
    @Body() batchDto: ExecuteBatchCommandsDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BatchCommandResultResponseDto>> {
    const { result, logIds } = await this.sshCommandService.executeBatchCommands(
      batchDto.sshServerId,
      batchDto.commands,
      user.sub,
      {
        executionType: batchDto.executionType,
        metadata: batchDto.metadata,
      },
    );

    const responseData: BatchCommandResultResponseDto = {
      logIds,
      totalCommands: result.totalCommands,
      successCount: result.successCount,
      failureCount: result.failureCount,
      results: result.results.map(cmdResult => ({
        logId: 0, // Will be filled from logIds
        command: cmdResult.command,
        stdout: cmdResult.stdout,
        stderr: cmdResult.stderr,
        exitCode: cmdResult.exitCode,
        executionTime: cmdResult.executionTime,
        success: cmdResult.success,
        error: cmdResult.error,
        executedAt: Date.now(),
        description: cmdResult.description,
      })),
      totalExecutionTime: result.totalExecutionTime,
      overallSuccess: result.overallSuccess,
      startedAt: Date.now() - result.totalExecutionTime,
      completedAt: Date.now(),
    };

    return {
      success: result.overallSuccess,
      message: result.overallSuccess 
        ? 'Batch commands thực thi thành công' 
        : `Batch commands hoàn thành với ${result.failureCount} lỗi`,
      data: responseData,
    };
  }

  /**
   * Deploy dự án
   */
  @Post('deploy')
  @ApiOperation({
    summary: 'Deploy dự án',
    description: 'Thực hiện deployment dự án lên server',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả deployment',
    type: ApiResponseDto<DeploymentStatusResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'SSH server không tồn tại',
  })
  async deployProject(
    @Body() deployDto: DeployProjectDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DeploymentStatusResponseDto>> {
    const deploymentConfig = {
      projectName: deployDto.projectName,
      deploymentType: deployDto.deploymentType,
      environment: deployDto.environment,
      projectPath: deployDto.projectPath,
      repositoryUrl: deployDto.repositoryUrl,
      branch: deployDto.branch,
      commitHash: deployDto.commitHash,
      tag: deployDto.tag,
      buildScript: deployDto.buildScript,
      postDeployScript: deployDto.postDeployScript,
      environmentVariables: deployDto.environmentVariables,
      additionalConfig: deployDto.additionalConfig,
      customCommands: deployDto.customCommands,
    };

    const result = await this.deploymentService.deploy(
      deployDto.sshServerId,
      deploymentConfig as any,
      user.sub,
    );

    const responseData: DeploymentStatusResponseDto = {
      deploymentId: result.deploymentId,
      projectName: deploymentConfig.projectName,
      deploymentType: deploymentConfig.deploymentType,
      environment: deploymentConfig.environment,
      status: result.status,
      branch: deploymentConfig.branch,
      commitHash: deploymentConfig.commitHash,
      tag: deploymentConfig.tag,
      startedAt: result.startedAt,
      completedAt: result.completedAt,
      executionTime: result.completedAt ? result.completedAt - result.startedAt : undefined,
      logs: result.logs,
      errorMessage: result.errorMessage,
      jobId: undefined,
      metadata: result.metadata,
      executionLogIds: result.executionLogIds,
      deployedBy: user.sub,
      createdAt: result.startedAt,
    };

    return {
      success: result.status === 'completed',
      message: result.status === 'completed' 
        ? 'Deployment thành công' 
        : `Deployment ${result.status}`,
      data: responseData,
    };
  }

  /**
   * Rollback deployment
   */
  @Post('rollback')
  @ApiOperation({
    summary: 'Rollback deployment',
    description: 'Rollback deployment về phiên bản trước đó',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả rollback',
    type: ApiResponseDto<DeploymentStatusResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Deployment không tồn tại',
  })
  async rollbackDeployment(
    @Body() rollbackDto: RollbackDeploymentDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DeploymentStatusResponseDto>> {
    const result = await this.deploymentService.rollback(
      rollbackDto.sshServerId,
      rollbackDto.deploymentId,
      user.sub,
    );

    // Get deployment info for response
    const deployment = await this.deploymentHistoryRepository.findById(result.deploymentId);

    const responseData: DeploymentStatusResponseDto = {
      deploymentId: result.deploymentId,
      projectName: deployment?.projectName || '',
      deploymentType: 'rollback',
      environment: deployment?.environment || 'production',
      status: result.status,
      startedAt: result.startedAt,
      completedAt: result.completedAt,
      executionTime: result.completedAt ? result.completedAt - result.startedAt : undefined,
      logs: result.logs,
      errorMessage: result.errorMessage,
      metadata: result.metadata,
      executionLogIds: result.executionLogIds,
      deployedBy: user.sub,
      createdAt: result.startedAt,
    };

    return {
      success: result.status === 'completed',
      message: result.status === 'completed' 
        ? 'Rollback thành công' 
        : `Rollback ${result.status}`,
      data: responseData,
    };
  }

  /**
   * Deploy với template
   */
  @Post('deploy-template')
  @ApiOperation({
    summary: 'Deploy với template',
    description: 'Thực hiện deployment sử dụng template có sẵn',
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả deployment template',
    type: ApiResponseDto<BatchCommandResultResponseDto>,
  })
  async deployWithTemplate(
    @Body() templateDto: DeploymentTemplateDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BatchCommandResultResponseDto>> {
    const { result, logIds } = await this.sshCommandService.executeDeploymentTemplate(
      templateDto.sshServerId,
      templateDto.templateName,
      user.sub,
      templateDto.customCommands,
      {
        metadata: {
          ...templateDto.metadata,
          projectName: templateDto.projectName,
          projectPath: templateDto.projectPath,
        },
      },
    );

    const responseData: BatchCommandResultResponseDto = {
      logIds,
      totalCommands: result.totalCommands,
      successCount: result.successCount,
      failureCount: result.failureCount,
      results: result.results.map(cmdResult => ({
        logId: 0,
        command: cmdResult.command,
        stdout: cmdResult.stdout,
        stderr: cmdResult.stderr,
        exitCode: cmdResult.exitCode,
        executionTime: cmdResult.executionTime,
        success: cmdResult.success,
        error: cmdResult.error,
        executedAt: Date.now(),
        description: cmdResult.description,
      })),
      totalExecutionTime: result.totalExecutionTime,
      overallSuccess: result.overallSuccess,
      startedAt: Date.now() - result.totalExecutionTime,
      completedAt: Date.now(),
    };

    return {
      success: result.overallSuccess,
      message: result.overallSuccess 
        ? 'Deployment template thành công' 
        : `Deployment template hoàn thành với ${result.failureCount} lỗi`,
      data: responseData,
    };
  }

  /**
   * Lấy danh sách execution logs
   */
  @Get('logs')
  @ApiOperation({
    summary: 'Lấy danh sách execution logs',
    description: 'Lấy danh sách logs thực thi lệnh SSH với phân trang',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (mặc định: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số items per page (mặc định: 10)' })
  @ApiQuery({ name: 'sshServerId', required: false, description: 'Lọc theo SSH server ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Lọc theo trạng thái' })
  @ApiQuery({ name: 'executionType', required: false, description: 'Lọc theo loại thực thi' })
  @ApiQuery({ name: 'search', required: false, description: 'Tìm kiếm trong command hoặc output' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách execution logs',
    type: ApiResponseDto<ExecutionLogListResponseDto>,
  })
  async getExecutionLogs(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sshServerId') sshServerId?: number,
    @Query('status') status?: string,
    @Query('executionType') executionType?: string,
    @Query('search') search?: string,
    @CurrentUser() user?: JwtPayload,
  ): Promise<ApiResponseDto<ExecutionLogListResponseDto>> {
    const pageNum = page || 1;
    const limitNum = limit || 10;

    const { items, total } = await this.sshExecutionLogRepository.findWithPagination({
      page: pageNum,
      limit: limitNum,
      sshServerId,
      status,
      executionType,
      search,
      executedBy: user?.sub,
    });

    const responseData: ExecutionLogListResponseDto = {
      items: items.map(log => this.mapExecutionLogToResponseDto(log)),
      total,
      page: pageNum,
      limit: limitNum,
      totalPages: Math.ceil(total / limitNum),
    };

    return {
      success: true,
      message: 'Lấy danh sách execution logs thành công',
      data: responseData,
    };
  }

  /**
   * Lấy execution log theo ID
   */
  @Get('logs/:id')
  @ApiOperation({
    summary: 'Lấy execution log theo ID',
    description: 'Lấy thông tin chi tiết execution log theo ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin execution log',
    type: ApiResponseDto<ExecutionLogResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Execution log không tồn tại',
  })
  async getExecutionLogById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<ExecutionLogResponseDto>> {
    const log = await this.sshExecutionLogRepository.findById(id);
    
    if (!log) {
      return {
        success: false,
        message: 'Execution log không tồn tại',
        data: null as any,
      };
    }

    return {
      success: true,
      message: 'Lấy thông tin execution log thành công',
      data: this.mapExecutionLogToResponseDto(log),
    };
  }

  /**
   * Lấy danh sách deployment templates
   */
  @Get('deployment-templates')
  @ApiOperation({
    summary: 'Lấy danh sách deployment templates',
    description: 'Lấy danh sách các template deployment có sẵn',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách deployment templates',
    type: ApiResponseDto<DeploymentTemplateListResponseDto>,
  })
  async getDeploymentTemplates(): Promise<ApiResponseDto<DeploymentTemplateListResponseDto>> {
    const templates = this.sshCommandService.getDeploymentTemplates();
    
    const responseData: DeploymentTemplateListResponseDto = {
      templates: Object.entries(templates).reduce((acc, [key, template]) => {
        acc[key] = {
          name: template.name,
          description: template.description,
          commands: template.commands,
        };
        return acc;
      }, {} as Record<string, DeploymentTemplateResponseDto>),
    };

    return {
      success: true,
      message: 'Lấy danh sách deployment templates thành công',
      data: responseData,
    };
  }

  /**
   * Map execution log entity to response DTO
   */
  private mapExecutionLogToResponseDto(log: any): ExecutionLogResponseDto {
    return {
      id: log.id,
      sshServerId: log.sshServerId,
      command: log.command,
      workingDirectory: log.workingDirectory,
      output: log.output,
      errorOutput: log.errorOutput,
      exitCode: log.exitCode,
      executionTimeMs: log.executionTimeMs,
      status: log.status,
      jobId: log.jobId,
      executionType: log.executionType,
      metadata: log.metadata ? JSON.parse(log.metadata) : null,
      executedBy: log.executedBy,
      executedAt: log.executedAt,
      completedAt: log.completedAt,
      createdAt: log.createdAt,
    };
  }
}
