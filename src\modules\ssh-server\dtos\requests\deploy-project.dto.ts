import { IsString, IsOptional, IsObject, IsEnum, IsArray, ValidateNested, IsNumber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { BatchCommandDto } from './execute-command.dto';

/**
 * DTO cho deployment dự án
 */
export class DeployProjectDto {
  /**
   * ID server SSH
   */
  @ApiProperty({
    description: 'ID server SSH',
    example: 1,
  })
  @IsNumber()
  sshServerId: number;

  /**
   * Tên dự án
   */
  @ApiProperty({
    description: 'Tên dự án',
    example: 'my-web-app',
  })
  @IsString()
  projectName: string;

  /**
   * Loại deployment
   */
  @ApiProperty({
    description: 'Loại deployment',
    enum: ['git_pull', 'git_clone', 'docker_build', 'npm_build', 'custom', 'rollback'],
    example: 'git_pull',
  })
  @IsEnum(['git_pull', 'git_clone', 'docker_build', 'npm_build', 'custom', 'rollback'])
  deploymentType: 'git_pull' | 'git_clone' | 'docker_build' | 'npm_build' | 'custom' | 'rollback';

  /**
   * Môi trường deployment
   */
  @ApiProperty({
    description: 'Môi trường deployment',
    enum: ['development', 'staging', 'production', 'testing'],
    example: 'production',
  })
  @IsEnum(['development', 'staging', 'production', 'testing'])
  environment: 'development' | 'staging' | 'production' | 'testing';

  /**
   * Thư mục dự án trên server
   */
  @ApiProperty({
    description: 'Thư mục dự án trên server',
    example: '/home/<USER>/my-web-app',
  })
  @IsString()
  projectPath: string;

  /**
   * URL repository (cho git_clone)
   */
  @ApiPropertyOptional({
    description: 'URL repository (cho git_clone)',
    example: 'https://github.com/user/repo.git',
  })
  @IsOptional()
  @IsString()
  repositoryUrl?: string;

  /**
   * Branch để deploy
   */
  @ApiPropertyOptional({
    description: 'Branch để deploy',
    example: 'main',
  })
  @IsOptional()
  @IsString()
  branch?: string;

  /**
   * Commit hash cụ thể
   */
  @ApiPropertyOptional({
    description: 'Commit hash cụ thể',
    example: 'abc123def456',
  })
  @IsOptional()
  @IsString()
  commitHash?: string;

  /**
   * Tag version
   */
  @ApiPropertyOptional({
    description: 'Tag version',
    example: 'v1.0.0',
  })
  @IsOptional()
  @IsString()
  tag?: string;

  /**
   * Script build tùy chỉnh
   */
  @ApiPropertyOptional({
    description: 'Script build tùy chỉnh',
    example: 'npm run build:prod',
  })
  @IsOptional()
  @IsString()
  buildScript?: string;

  /**
   * Script post-deployment
   */
  @ApiPropertyOptional({
    description: 'Script post-deployment',
    example: 'pm2 restart app',
  })
  @IsOptional()
  @IsString()
  postDeployScript?: string;

  /**
   * Biến môi trường
   */
  @ApiPropertyOptional({
    description: 'Biến môi trường',
    example: { NODE_ENV: 'production', PORT: '3000' },
  })
  @IsOptional()
  @IsObject()
  environmentVariables?: Record<string, string>;

  /**
   * Cấu hình bổ sung
   */
  @ApiPropertyOptional({
    description: 'Cấu hình bổ sung',
    example: { packageManager: 'npm', pm2AppName: 'my-app' },
  })
  @IsOptional()
  @IsObject()
  additionalConfig?: Record<string, any>;

  /**
   * Lệnh tùy chỉnh (cho deployment type 'custom')
   */
  @ApiPropertyOptional({
    description: 'Lệnh tùy chỉnh (cho deployment type custom)',
    type: [BatchCommandDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchCommandDto)
  customCommands?: BatchCommandDto[];
}

/**
 * DTO cho rollback deployment
 */
export class RollbackDeploymentDto {
  /**
   * ID server SSH
   */
  @ApiProperty({
    description: 'ID server SSH',
    example: 1,
  })
  @IsNumber()
  sshServerId: number;

  /**
   * ID deployment cần rollback
   */
  @ApiProperty({
    description: 'ID deployment cần rollback',
    example: 123,
  })
  @IsNumber()
  deploymentId: number;

  /**
   * Có tạo backup trước rollback không
   */
  @ApiPropertyOptional({
    description: 'Có tạo backup trước rollback không',
    example: true,
    default: true,
  })
  @IsOptional()
  createBackupBeforeRollback?: boolean = true;
}

/**
 * DTO cho deployment template
 */
export class DeploymentTemplateDto {
  /**
   * ID server SSH
   */
  @ApiProperty({
    description: 'ID server SSH',
    example: 1,
  })
  @IsNumber()
  sshServerId: number;

  /**
   * Tên template
   */
  @ApiProperty({
    description: 'Tên template',
    enum: ['NODEJS', 'REACT', 'DOCKER', 'NESTJS', 'CUSTOM'],
    example: 'NODEJS',
  })
  @IsEnum(['NODEJS', 'REACT', 'DOCKER', 'NESTJS', 'CUSTOM'])
  templateName: 'NODEJS' | 'REACT' | 'DOCKER' | 'NESTJS' | 'CUSTOM';

  /**
   * Tên dự án
   */
  @ApiProperty({
    description: 'Tên dự án',
    example: 'my-nodejs-app',
  })
  @IsString()
  projectName: string;

  /**
   * Thư mục dự án
   */
  @ApiProperty({
    description: 'Thư mục dự án',
    example: '/home/<USER>/my-nodejs-app',
  })
  @IsString()
  projectPath: string;

  /**
   * Lệnh tùy chỉnh (cho template CUSTOM)
   */
  @ApiPropertyOptional({
    description: 'Lệnh tùy chỉnh (cho template CUSTOM)',
    type: [BatchCommandDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchCommandDto)
  customCommands?: BatchCommandDto[];

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { pm2AppName: 'my-app' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
