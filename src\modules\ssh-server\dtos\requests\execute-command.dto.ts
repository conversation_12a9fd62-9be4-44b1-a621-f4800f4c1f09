import { IsString, IsOptional, IsObject, IsNumber, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

/**
 * DTO cho thực thi lệnh SSH đơn lẻ
 */
export class ExecuteCommandDto {
  /**
   * ID server SSH
   */
  @ApiProperty({
    description: 'ID server SSH',
    example: 1,
  })
  @IsNumber()
  sshServerId: number;

  /**
   * <PERSON>ệnh cần thực thi
   */
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> cần thực thi',
    example: 'ls -la',
  })
  @IsString()
  command: string;

  /**
   * Thư mục làm việc
   */
  @ApiPropertyOptional({
    description: 'Th<PERSON> mục làm việc',
    example: '/home/<USER>/project',
  })
  @IsOptional()
  @IsString()
  workingDirectory?: string;

  /**
   * <PERSON>iế<PERSON> môi trường
   */
  @ApiPropertyOptional({
    description: 'Biến môi trường',
    example: { NODE_ENV: 'production', PORT: '3000' },
  })
  @IsOptional()
  @IsObject()
  environment?: Record<string, string>;

  /**
   * Timeout thực thi (milliseconds)
   */
  @ApiPropertyOptional({
    description: 'Timeout thực thi (milliseconds)',
    example: 300000,
  })
  @IsOptional()
  @IsNumber()
  timeout?: number;

  /**
   * Loại thực thi
   */
  @ApiPropertyOptional({
    description: 'Loại thực thi',
    enum: ['manual', 'scheduled', 'deployment', 'health_check', 'batch'],
    example: 'manual',
  })
  @IsOptional()
  @IsEnum(['manual', 'scheduled', 'deployment', 'health_check', 'batch'])
  executionType?: 'manual' | 'scheduled' | 'deployment' | 'health_check' | 'batch';

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { description: 'Check system status' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho batch command
 */
export class BatchCommandDto {
  /**
   * Lệnh
   */
  @ApiProperty({
    description: 'Lệnh',
    example: 'git pull',
  })
  @IsString()
  command: string;

  /**
   * Mô tả lệnh
   */
  @ApiPropertyOptional({
    description: 'Mô tả lệnh',
    example: 'Pull latest code from repository',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Thư mục làm việc
   */
  @ApiPropertyOptional({
    description: 'Thư mục làm việc',
    example: '/home/<USER>/project',
  })
  @IsOptional()
  @IsString()
  workingDirectory?: string;

  /**
   * Biến môi trường
   */
  @ApiPropertyOptional({
    description: 'Biến môi trường',
    example: { NODE_ENV: 'production' },
  })
  @IsOptional()
  @IsObject()
  environment?: Record<string, string>;

  /**
   * Có tiếp tục nếu lệnh này thất bại
   */
  @ApiPropertyOptional({
    description: 'Có tiếp tục nếu lệnh này thất bại',
    example: false,
  })
  @IsOptional()
  continueOnError?: boolean;
}

/**
 * DTO cho thực thi batch commands
 */
export class ExecuteBatchCommandsDto {
  /**
   * ID server SSH
   */
  @ApiProperty({
    description: 'ID server SSH',
    example: 1,
  })
  @IsNumber()
  sshServerId: number;

  /**
   * Danh sách lệnh
   */
  @ApiProperty({
    description: 'Danh sách lệnh',
    type: [BatchCommandDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchCommandDto)
  commands: BatchCommandDto[];

  /**
   * Loại thực thi
   */
  @ApiPropertyOptional({
    description: 'Loại thực thi',
    enum: ['manual', 'scheduled', 'deployment', 'health_check', 'batch'],
    example: 'batch',
  })
  @IsOptional()
  @IsEnum(['manual', 'scheduled', 'deployment', 'health_check', 'batch'])
  executionType?: 'manual' | 'scheduled' | 'deployment' | 'health_check' | 'batch';

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: { batchName: 'Deploy to production' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
