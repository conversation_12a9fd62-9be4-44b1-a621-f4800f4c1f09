import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsB<PERSON>ean, <PERSON>, <PERSON>, Length } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho tạo SSH server mới
 */
export class CreateSshServerDto {
  /**
   * Tên server SSH (duy nhất trong tenant)
   */
  @ApiProperty({
    description: 'Tên server SSH (duy nhất trong tenant)',
    example: 'Production Server 1',
    minLength: 1,
    maxLength: 255,
  })
  @IsString()
  @Length(1, 255)
  name: string;

  /**
   * Địa chỉ IP hoặc hostname của server
   */
  @ApiProperty({
    description: 'Địa chỉ IP hoặc hostname của server',
    example: '*************',
  })
  @IsString()
  @Length(1, 255)
  host: string;

  /**
   * Port SSH (mặc định 22)
   */
  @ApiPropertyOptional({
    description: 'Port SSH',
    example: 22,
    minimum: 1,
    maximum: 65535,
    default: 22,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(65535)
  port?: number = 22;

  /**
   * Username để đăng nhập SSH
   */
  @ApiProperty({
    description: 'Username để đăng nhập SSH',
    example: 'ubuntu',
  })
  @IsString()
  @Length(1, 255)
  username: string;

  /**
   * Password để đăng nhập SSH (sẽ được mã hóa)
   */
  @ApiPropertyOptional({
    description: 'Password để đăng nhập SSH (sẽ được mã hóa)',
    example: 'your-secure-password',
  })
  @IsOptional()
  @IsString()
  password?: string;

  /**
   * Private key để đăng nhập SSH (sẽ được mã hóa)
   */
  @ApiPropertyOptional({
    description: 'Private key để đăng nhập SSH (sẽ được mã hóa)',
    example: '-----BEGIN OPENSSH PRIVATE KEY-----\n...\n-----END OPENSSH PRIVATE KEY-----',
  })
  @IsOptional()
  @IsString()
  privateKey?: string;

  /**
   * Passphrase cho private key (sẽ được mã hóa)
   */
  @ApiPropertyOptional({
    description: 'Passphrase cho private key (sẽ được mã hóa)',
    example: 'passphrase-for-private-key',
  })
  @IsOptional()
  @IsString()
  passphrase?: string;

  /**
   * Mô tả server
   */
  @ApiPropertyOptional({
    description: 'Mô tả server',
    example: 'Production server for web application',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Thời gian timeout kết nối (milliseconds)
   */
  @ApiPropertyOptional({
    description: 'Thời gian timeout kết nối (milliseconds)',
    example: 30000,
    minimum: 5000,
    maximum: 300000,
    default: 30000,
  })
  @IsOptional()
  @IsNumber()
  @Min(5000)
  @Max(300000)
  connectionTimeout?: number = 30000;

  /**
   * Thời gian timeout thực thi lệnh (milliseconds)
   */
  @ApiPropertyOptional({
    description: 'Thời gian timeout thực thi lệnh (milliseconds)',
    example: 300000,
    minimum: 10000,
    maximum: 3600000,
    default: 300000,
  })
  @IsOptional()
  @IsNumber()
  @Min(10000)
  @Max(3600000)
  commandTimeout?: number = 300000;

  /**
   * Số lần thử lại khi kết nối thất bại
   */
  @ApiPropertyOptional({
    description: 'Số lần thử lại khi kết nối thất bại',
    example: 3,
    minimum: 1,
    maximum: 10,
    default: 3,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  retryAttempts?: number = 3;

  /**
   * Thời gian delay giữa các lần thử lại (milliseconds)
   */
  @ApiPropertyOptional({
    description: 'Thời gian delay giữa các lần thử lại (milliseconds)',
    example: 5000,
    minimum: 1000,
    maximum: 60000,
    default: 5000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(60000)
  retryDelay?: number = 5000;

  /**
   * Trạng thái hoạt động của server
   */
  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của server',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}
