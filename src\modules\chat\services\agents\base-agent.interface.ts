/**
 * Interface cơ bản cho tất cả agents trong hệ thống multi-agent
 */
export interface BaseAgent {
  /**
   * Tên duy nhất của agent
   */
  readonly name: string;

  /**
   * <PERSON><PERSON> tả chức năng của agent
   */
  readonly description: string;

  /**
   * Danh sách keywords mà agent có thể xử lý
   */
  readonly keywords: string[];

  /**
   * Độ ưu tiên của agent (1-10, 10 là cao nhất)
   */
  readonly priority: number;

  /**
   * Kiểm tra xem agent có thể xử lý request này không
   * @param message Tin nhắn từ user
   * @param context Context của cuộc hội thoại
   * @returns Điểm confidence (0-1)
   */
  canHandle(message: string, context?: any): Promise<number>;

  /**
   * Xử lý request từ user
   * @param message Tin nhắn từ user
   * @param context Context của cuộc hội thoại
   * @param config Cấu hình runtime
   * @returns Kết quả xử lý
   */
  process(
    message: string,
    context?: any,
    config?: AgentConfig,
  ): Promise<AgentResponse>;

  /**
   * L<PERSON>y danh sách tools mà agent sử dụng
   */
  getTools(): any[];

  /**
   * Khởi tạo agent (nếu cần)
   */
  initialize?(): Promise<void>;

  /**
   * Dọn dẹp tài nguyên (nếu cần)
   */
  cleanup?(): Promise<void>;
}

/**
 * Cấu hình runtime cho agent
 */
export interface AgentConfig {
  tenantId: number;
  userId: number;
  threadId?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  [key: string]: any;
}

/**
 * Response từ agent
 */
export interface AgentResponse {
  /**
   * Kết quả xử lý
   */
  result: string;

  /**
   * Độ tin cậy của kết quả (0-1)
   */
  confidence: number;

  /**
   * Metadata bổ sung
   */
  metadata?: {
    toolsUsed?: string[];
    executionTime?: number;
    tokensUsed?: number;
    [key: string]: any;
  };

  /**
   * Có cần chuyển tiếp cho agent khác không
   */
  needsHandoff?: boolean;

  /**
   * Agent được đề xuất để xử lý tiếp
   */
  suggestedAgent?: string;

  /**
   * Lỗi nếu có
   */
  error?: string;
}

/**
 * Intent được phân tích từ tin nhắn user
 */
export interface UserIntent {
  /**
   * Loại intent chính
   */
  category: IntentCategory;

  /**
   * Intent cụ thể
   */
  action: string;

  /**
   * Entities được trích xuất
   */
  entities: Record<string, any>;

  /**
   * Độ tin cậy của việc phân tích intent
   */
  confidence: number;

  /**
   * Context bổ sung
   */
  context?: Record<string, any>;
}

/**
 * Các loại intent chính
 */
export enum IntentCategory {
  HRM = 'hrm',
  PROJECT = 'project',
  COMMUNICATION = 'communication',
  ANALYTICS = 'analytics',
  DOCUMENT = 'document',
  INFRASTRUCTURE = 'infrastructure',
  GENERAL = 'general',
  UNKNOWN = 'unknown',
}

/**
 * Kết quả routing từ coordinator
 */
export interface RoutingResult {
  /**
   * Agent được chọn
   */
  selectedAgent: string;

  /**
   * Độ tin cậy của việc routing
   */
  confidence: number;

  /**
   * Lý do chọn agent này
   */
  reason: string;

  /**
   * Các agent backup nếu agent chính thất bại
   */
  fallbackAgents?: string[];
}
