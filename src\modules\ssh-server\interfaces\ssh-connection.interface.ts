/**
 * Interface cho cấu hình kết nối SSH
 */
export interface SshConnectionConfig {
  /** Địa chỉ host */
  host: string;
  /** Port SSH */
  port: number;
  /** Username */
  username: string;
  /** Password (plain text) */
  password?: string;
  /** Private key (plain text) */
  privateKey?: string;
  /** Passphrase cho private key */
  passphrase?: string;
  /** Timeout kết nối (ms) */
  connectionTimeout?: number;
  /** Timeout thực thi lệnh (ms) */
  commandTimeout?: number;
  /** Số lần thử lại */
  retryAttempts?: number;
  /** Delay giữa các lần thử lại (ms) */
  retryDelay?: number;
}

/**
 * Interface cho kết quả thực thi lệnh SSH
 */
export interface SshCommandResult {
  /** Lệnh đã thực thi */
  command: string;
  /** Output của lệnh */
  stdout: string;
  /** Error output của lệnh */
  stderr: string;
  /** Exit code */
  exitCode: number;
  /** Thời gian thực thi (ms) */
  executionTime: number;
  /** Trạng thái thực thi */
  success: boolean;
  /** Thông báo lỗi (nếu có) */
  error?: string;
}

/**
 * Interface cho thông tin kết nối SSH
 */
export interface SshConnectionInfo {
  /** ID kết nối */
  connectionId: string;
  /** Thông tin server */
  server: {
    host: string;
    port: number;
    username: string;
  };
  /** Trạng thái kết nối */
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  /** Thời gian kết nối */
  connectedAt?: number;
  /** Thời gian ngắt kết nối */
  disconnectedAt?: number;
  /** Lỗi kết nối */
  error?: string;
}

/**
 * Interface cho tùy chọn thực thi lệnh
 */
export interface SshExecutionOptions {
  /** Thư mục làm việc */
  workingDirectory?: string;
  /** Biến môi trường */
  environment?: Record<string, string>;
  /** Timeout thực thi (ms) */
  timeout?: number;
  /** Có stream output real-time không */
  streaming?: boolean;
  /** Callback cho output streaming */
  onOutput?: (data: string) => void;
  /** Callback cho error output streaming */
  onError?: (data: string) => void;
}

/**
 * Interface cho batch command execution
 */
export interface SshBatchCommand {
  /** Lệnh */
  command: string;
  /** Thư mục làm việc */
  workingDirectory?: string;
  /** Biến môi trường */
  environment?: Record<string, string>;
  /** Có tiếp tục nếu lệnh này thất bại */
  continueOnError?: boolean;
  /** Mô tả lệnh */
  description?: string;
}

/**
 * Interface cho kết quả batch execution
 */
export interface SshBatchResult {
  /** Tổng số lệnh */
  totalCommands: number;
  /** Số lệnh thành công */
  successCount: number;
  /** Số lệnh thất bại */
  failureCount: number;
  /** Kết quả từng lệnh */
  results: Array<SshCommandResult & { description?: string }>;
  /** Tổng thời gian thực thi */
  totalExecutionTime: number;
  /** Trạng thái tổng thể */
  overallSuccess: boolean;
}

/**
 * Interface cho SSH health check
 */
export interface SshHealthCheck {
  /** Trạng thái kết nối */
  connectionStatus: 'healthy' | 'unhealthy' | 'unknown';
  /** Thời gian response (ms) */
  responseTime: number;
  /** Thông tin hệ thống */
  systemInfo?: {
    uptime?: string;
    loadAverage?: string;
    memoryUsage?: string;
    diskUsage?: string;
  };
  /** Thời gian check */
  checkedAt: number;
  /** Lỗi (nếu có) */
  error?: string;
}

/**
 * Interface cho SSH connection pool
 */
export interface SshConnectionPool {
  /** Số kết nối tối đa */
  maxConnections: number;
  /** Số kết nối hiện tại */
  activeConnections: number;
  /** Số kết nối đang chờ */
  pendingConnections: number;
  /** Thời gian idle timeout (ms) */
  idleTimeout: number;
}
