import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { SshServerService } from '../services/ssh-server.service';
import { CreateSshServerDto } from '../dtos/requests/create-ssh-server.dto';
import { UpdateSshServerDto } from '../dtos/requests/update-ssh-server.dto';

/**
 * Tool để quản lý SSH servers thông qua chat interface
 */
@Injectable()
export class SshServerManagementTool {
  private readonly logger = new Logger(SshServerManagementTool.name);

  constructor(private readonly sshServerService: SshServerService) {}

  /**
   * L<PERSON>y danh sách SSH servers
   */
  async listSshServers(params: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }) {
    try {
      const { items, total } = await this.sshServerService.findWithPagination({
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search,
        isActive: params.isActive,
      });

      return {
        success: true,
        message: `Tìm thấy ${total} SSH servers`,
        data: {
          servers: items.map(server => ({
            id: server.id,
            name: server.name,
            host: server.host,
            port: server.port,
            username: server.username,
            description: server.description,
            isActive: server.isActive,
            lastConnectionStatus: server.lastConnectionStatus,
            lastConnectedAt: server.lastConnectedAt,
            createdAt: server.createdAt,
          })),
          total,
          page: params.page || 1,
          limit: params.limit || 10,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy danh sách SSH servers: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy danh sách SSH servers: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy thông tin SSH server theo ID
   */
  async getSshServerById(serverId: number) {
    try {
      const server = await this.sshServerService.findById(serverId);
      
      if (!server) {
        return {
          success: false,
          message: `SSH server với ID ${serverId} không tồn tại`,
          data: null,
        };
      }

      return {
        success: true,
        message: `Thông tin SSH server ${server.name}`,
        data: {
          id: server.id,
          name: server.name,
          host: server.host,
          port: server.port,
          username: server.username,
          description: server.description,
          isActive: server.isActive,
          connectionTimeout: server.connectionTimeout,
          commandTimeout: server.commandTimeout,
          retryAttempts: server.retryAttempts,
          retryDelay: server.retryDelay,
          lastConnectionStatus: server.lastConnectionStatus,
          lastConnectedAt: server.lastConnectedAt,
          lastConnectionError: server.lastConnectionError,
          createdAt: server.createdAt,
          updatedAt: server.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy thông tin SSH server: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy thông tin SSH server: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Tạo SSH server mới
   */
  async createSshServer(params: {
    name: string;
    host: string;
    port?: number;
    username: string;
    password?: string;
    privateKey?: string;
    passphrase?: string;
    description?: string;
    connectionTimeout?: number;
    commandTimeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
    isActive?: boolean;
  }, createdBy: number) {
    try {
      const createDto: CreateSshServerDto = {
        name: params.name,
        host: params.host,
        port: params.port || 22,
        username: params.username,
        password: params.password,
        privateKey: params.privateKey,
        passphrase: params.passphrase,
        description: params.description,
        connectionTimeout: params.connectionTimeout || 30000,
        commandTimeout: params.commandTimeout || 300000,
        retryAttempts: params.retryAttempts || 3,
        retryDelay: params.retryDelay || 5000,
        isActive: params.isActive !== false,
      };

      const server = await this.sshServerService.create(createDto, createdBy);

      return {
        success: true,
        message: `SSH server "${server.name}" đã được tạo thành công`,
        data: {
          id: server.id,
          name: server.name,
          host: server.host,
          port: server.port,
          username: server.username,
          description: server.description,
          isActive: server.isActive,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi tạo SSH server: ${error.message}`);
      return {
        success: false,
        message: `Lỗi tạo SSH server: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Cập nhật SSH server
   */
  async updateSshServer(
    serverId: number,
    params: {
      name?: string;
      host?: string;
      port?: number;
      username?: string;
      password?: string;
      privateKey?: string;
      passphrase?: string;
      description?: string;
      connectionTimeout?: number;
      commandTimeout?: number;
      retryAttempts?: number;
      retryDelay?: number;
      isActive?: boolean;
    },
    updatedBy: number,
  ) {
    try {
      const updateDto: UpdateSshServerDto = {
        ...params,
      };

      const server = await this.sshServerService.update(serverId, updateDto, updatedBy);

      return {
        success: true,
        message: `SSH server "${server.name}" đã được cập nhật thành công`,
        data: {
          id: server.id,
          name: server.name,
          host: server.host,
          port: server.port,
          username: server.username,
          description: server.description,
          isActive: server.isActive,
          updatedAt: server.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi cập nhật SSH server: ${error.message}`);
      return {
        success: false,
        message: `Lỗi cập nhật SSH server: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Xóa SSH server
   */
  async deleteSshServer(serverId: number) {
    try {
      await this.sshServerService.softDelete(serverId);

      return {
        success: true,
        message: `SSH server đã được xóa thành công`,
        data: { deletedId: serverId },
      };
    } catch (error) {
      this.logger.error(`Lỗi xóa SSH server: ${error.message}`);
      return {
        success: false,
        message: `Lỗi xóa SSH server: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Test kết nối SSH server
   */
  async testSshConnection(serverId: number) {
    try {
      const result = await this.sshServerService.testConnection(serverId);

      return {
        success: result.success,
        message: result.message,
        data: {
          serverId,
          connectionStatus: result.success ? 'connected' : 'failed',
          responseTime: result.responseTime,
          systemInfo: result.systemInfo,
          error: result.error,
          testedAt: result.testedAt,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi test kết nối SSH: ${error.message}`);
      return {
        success: false,
        message: `Lỗi test kết nối SSH: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Kích hoạt SSH server
   */
  async activateSshServer(serverId: number, updatedBy: number) {
    try {
      await this.sshServerService.activate(serverId, updatedBy);

      return {
        success: true,
        message: `SSH server đã được kích hoạt`,
        data: { serverId, isActive: true },
      };
    } catch (error) {
      this.logger.error(`Lỗi kích hoạt SSH server: ${error.message}`);
      return {
        success: false,
        message: `Lỗi kích hoạt SSH server: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Vô hiệu hóa SSH server
   */
  async deactivateSshServer(serverId: number, updatedBy: number) {
    try {
      await this.sshServerService.deactivate(serverId, updatedBy);

      return {
        success: true,
        message: `SSH server đã được vô hiệu hóa`,
        data: { serverId, isActive: false },
      };
    } catch (error) {
      this.logger.error(`Lỗi vô hiệu hóa SSH server: ${error.message}`);
      return {
        success: false,
        message: `Lỗi vô hiệu hóa SSH server: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy thống kê SSH servers
   */
  async getSshServerStatistics() {
    try {
      const statistics = await this.sshServerService.getStatistics();

      return {
        success: true,
        message: 'Thống kê SSH servers',
        data: statistics,
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy thống kê SSH servers: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy thống kê SSH servers: ${error.message}`,
        data: null,
      };
    }
  }

  /**
   * Lấy danh sách SSH servers đang hoạt động
   */
  async getActiveSshServers() {
    try {
      const servers = await this.sshServerService.findAllActive();

      return {
        success: true,
        message: `Tìm thấy ${servers.length} SSH servers đang hoạt động`,
        data: {
          servers: servers.map(server => ({
            id: server.id,
            name: server.name,
            host: server.host,
            port: server.port,
            username: server.username,
            description: server.description,
            lastConnectionStatus: server.lastConnectionStatus,
            lastConnectedAt: server.lastConnectedAt,
          })),
          total: servers.length,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi lấy danh sách SSH servers hoạt động: ${error.message}`);
      return {
        success: false,
        message: `Lỗi lấy danh sách SSH servers hoạt động: ${error.message}`,
        data: null,
      };
    }
  }
}
