import { Injectable, Logger } from '@nestjs/common';
import { QueueService } from '@/shared/queue/queue.service';
import { SshExecutionJobName, DeploymentJobName } from '@/shared/queue/queue.constants';
import { AnyDeploymentConfig } from '../interfaces/deployment-config.interface';

/**
 * Service quản lý SSH queue operations
 */
@Injectable()
export class SshQueueService {
  private readonly logger = new Logger(SshQueueService.name);

  constructor(private readonly queueService: QueueService) {}

  /**
   * Thêm job thực thi lệnh SSH vào queue
   */
  async addExecuteCommandJob(
    sshServerId: number,
    command: string,
    executedBy: number,
    options?: {
      workingDirectory?: string;
      environment?: Record<string, string>;
      timeout?: number;
      executionType?: string;
      metadata?: any;
      priority?: number;
      delay?: number;
    },
  ): Promise<string | undefined> {
    try {
      const jobData = {
        jobName: SshExecutionJobName.EXECUTE_COMMAND,
        sshServerId,
        command,
        executedBy,
        options,
      };

      const jobOptions = {
        priority: options?.priority || 0,
        delay: options?.delay || 0,
        removeOnComplete: 20,
        removeOnFail: 50,
      };

      const jobId = await this.queueService.addSshExecutionJob(jobData, jobOptions);
      
      this.logger.log(`Đã thêm SSH command job vào queue: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi thêm SSH command job vào queue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm job thực thi batch commands vào queue
   */
  async addExecuteBatchCommandsJob(
    sshServerId: number,
    commands: Array<{
      command: string;
      description?: string;
      workingDirectory?: string;
      environment?: Record<string, string>;
      continueOnError?: boolean;
    }>,
    executedBy: number,
    options?: {
      executionType?: string;
      metadata?: any;
      priority?: number;
      delay?: number;
    },
  ): Promise<string | undefined> {
    try {
      const jobData = {
        jobName: SshExecutionJobName.EXECUTE_BATCH_COMMANDS,
        sshServerId,
        commands,
        executedBy,
        options,
      };

      const jobOptions = {
        priority: options?.priority || 0,
        delay: options?.delay || 0,
        removeOnComplete: 20,
        removeOnFail: 50,
        timeout: 600000, // 10 minutes for batch commands
      };

      const jobId = await this.queueService.addSshExecutionJob(jobData, jobOptions);
      
      this.logger.log(`Đã thêm SSH batch commands job vào queue: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi thêm SSH batch commands job vào queue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm job health check SSH server vào queue
   */
  async addHealthCheckJob(
    sshServerId: number,
    executedBy: number,
    options?: {
      priority?: number;
      delay?: number;
    },
  ): Promise<string | undefined> {
    try {
      const jobData = {
        jobName: SshExecutionJobName.HEALTH_CHECK,
        sshServerId,
        executedBy,
      };

      const jobOptions = {
        priority: options?.priority || 5, // Higher priority for health checks
        delay: options?.delay || 0,
        removeOnComplete: 10,
        removeOnFail: 20,
        timeout: 120000, // 2 minutes for health check
      };

      const jobId = await this.queueService.addSshExecutionJob(jobData, jobOptions);
      
      this.logger.log(`Đã thêm SSH health check job vào queue: ${jobId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi thêm SSH health check job vào queue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm job deploy project vào queue
   */
  async addDeployProjectJob(
    sshServerId: number,
    deploymentConfig: AnyDeploymentConfig,
    deployedBy: number,
    options?: {
      priority?: number;
      delay?: number;
    },
  ): Promise<string | undefined> {
    try {
      const jobData = {
        jobName: DeploymentJobName.DEPLOY_PROJECT,
        sshServerId,
        deploymentConfig,
        deployedBy,
      };

      const jobOptions = {
        priority: options?.priority || 10, // High priority for deployments
        delay: options?.delay || 0,
        removeOnComplete: 50,
        removeOnFail: 100,
        timeout: 1800000, // 30 minutes for deployment
        attempts: 1, // No retry for deployments
      };

      const jobId = await this.queueService.addDeploymentJob(jobData, jobOptions);
      
      this.logger.log(`Đã thêm deployment job vào queue: ${jobId} - Project: ${deploymentConfig.projectName}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi thêm deployment job vào queue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm job rollback deployment vào queue
   */
  async addRollbackDeploymentJob(
    sshServerId: number,
    deploymentId: number,
    deployedBy: number,
    options?: {
      priority?: number;
      delay?: number;
    },
  ): Promise<string | undefined> {
    try {
      const jobData = {
        jobName: DeploymentJobName.ROLLBACK_DEPLOYMENT,
        sshServerId,
        deploymentId,
        deployedBy,
      };

      const jobOptions = {
        priority: options?.priority || 15, // Very high priority for rollbacks
        delay: options?.delay || 0,
        removeOnComplete: 50,
        removeOnFail: 100,
        timeout: 1200000, // 20 minutes for rollback
        attempts: 1, // No retry for rollbacks
      };

      const jobId = await this.queueService.addDeploymentJob(jobData, jobOptions);
      
      this.logger.log(`Đã thêm rollback job vào queue: ${jobId} - Deployment: ${deploymentId}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi thêm rollback job vào queue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm job deploy với template vào queue
   */
  async addDeployWithTemplateJob(
    sshServerId: number,
    templateName: string,
    projectName: string,
    executedBy: number,
    customCommands?: Array<{
      command: string;
      description?: string;
      workingDirectory?: string;
      environment?: Record<string, string>;
      continueOnError?: boolean;
    }>,
    options?: {
      metadata?: any;
      priority?: number;
      delay?: number;
    },
  ): Promise<string | undefined> {
    try {
      const jobData = {
        jobName: DeploymentJobName.DEPLOY_WITH_TEMPLATE,
        sshServerId,
        templateName,
        projectName,
        customCommands,
        executedBy,
        metadata: options?.metadata,
      };

      const jobOptions = {
        priority: options?.priority || 8,
        delay: options?.delay || 0,
        removeOnComplete: 30,
        removeOnFail: 50,
        timeout: 900000, // 15 minutes for template deployment
      };

      const jobId = await this.queueService.addDeploymentJob(jobData, jobOptions);
      
      this.logger.log(`Đã thêm deployment template job vào queue: ${jobId} - Template: ${templateName}`);
      return jobId;
    } catch (error) {
      this.logger.error(`Lỗi thêm deployment template job vào queue: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy thông tin job từ queue
   */
  async getJobInfo(queueName: string, jobId: string) {
    try {
      return await this.queueService.getJobInfo(queueName, jobId);
    } catch (error) {
      this.logger.error(`Lỗi lấy thông tin job: ${error.message}`);
      throw error;
    }
  }

  /**
   * Hủy job trong queue
   */
  async cancelJob(queueName: string, jobId: string) {
    try {
      await this.queueService.removeJob(queueName, jobId);
      this.logger.log(`Đã hủy job: ${jobId} trong queue: ${queueName}`);
    } catch (error) {
      this.logger.error(`Lỗi hủy job: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy thống kê queue
   */
  async getQueueStatistics(queueName: string) {
    try {
      return await this.queueService.getQueueStatistics(queueName);
    } catch (error) {
      this.logger.error(`Lỗi lấy thống kê queue: ${error.message}`);
      throw error;
    }
  }
}
