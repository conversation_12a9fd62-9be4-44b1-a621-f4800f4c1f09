import { Injectable, Logger } from '@nestjs/common';
import { ChatOpenAI } from '@langchain/openai';
import { createReactAgent } from '@langchain/langgraph/prebuilt';
import { MemorySaver } from '@langchain/langgraph';
import { HumanMessage } from '@langchain/core/messages';
import { 
  BaseAgent, 
  AgentConfig, 
  AgentResponse, 
  UserIntent, 
  IntentCategory 
} from './base-agent.interface';
import { SshToolsProvider } from '@/modules/ssh-server/tools/ssh-tools.provider';

/**
 * SSH Agent - Chuyên xử lý các tác vụ liên quan đến SSH server management và deployment
 */
@Injectable()
export class SshAgentService implements BaseAgent {
  private readonly logger = new Logger(SshAgentService.name);
  private agent: any;
  private memory: MemorySaver;

  readonly name = 'ssh-agent';
  readonly description = 'Agent chuyên xử lý SSH server management, command execution và deployment automation';
  readonly priority = 0.8;

  constructor(private readonly sshToolsProvider: SshToolsProvider) {
    this.memory = new MemorySaver();
    this.initializeAgent();
  }

  /**
   * Khởi tạo SSH agent với tools
   */
  private async initializeAgent() {
    try {
      const model = new ChatOpenAI({
        modelName: 'gpt-4o-mini',
        temperature: 0.1,
        maxTokens: 4000,
      });

      const tools = this.getTools();
      
      this.agent = createReactAgent({
        llm: model,
        tools,
        checkpointSaver: this.memory,
        messageModifier: `Bạn là SSH Agent - một chuyên gia về quản lý SSH server và deployment automation.

Nhiệm vụ của bạn:
1. Quản lý SSH servers (tạo, cập nhật, xóa, test kết nối)
2. Thực thi lệnh SSH trên các servers
3. Thực hiện deployment automation
4. Hỗ trợ rollback deployments
5. Cung cấp thống kê và monitoring

Khi người dùng hỏi về:
- SSH servers: Sử dụng SSH server management tools
- Thực thi lệnh: Sử dụng SSH command execution tools  
- Deployment: Sử dụng deployment automation tools
- Thống kê: Cung cấp thông tin chi tiết về servers và deployments

Luôn:
- Kiểm tra quyền truy cập trước khi thực hiện tác vụ
- Cung cấp thông tin chi tiết về kết quả
- Đề xuất best practices cho SSH security
- Cảnh báo về các rủi ro khi thực hiện deployment

Trả lời bằng tiếng Việt và cung cấp thông tin chính xác, hữu ích.`,
      });

      this.logger.log('SSH Agent đã được khởi tạo thành công');
    } catch (error) {
      this.logger.error(`Lỗi khởi tạo SSH Agent: ${error.message}`, error.stack);
    }
  }

  /**
   * Lấy danh sách tools của SSH agent
   */
  getTools() {
    return this.sshToolsProvider.getTools();
  }

  /**
   * Kiểm tra xem agent có thể xử lý request không
   */
  async canHandle(message: string, context?: any): Promise<number> {
    const normalizedMessage = message.toLowerCase();
    
    // SSH-related keywords
    const sshKeywords = [
      'ssh', 'server', 'máy chủ', 'kết nối', 'connection',
      'deploy', 'deployment', 'triển khai', 'rollback',
      'command', 'lệnh', 'thực thi', 'execute',
      'terminal', 'shell', 'bash', 'linux',
      'git pull', 'git clone', 'docker', 'npm',
      'pm2', 'systemctl', 'service',
      'production', 'staging', 'development',
      'build', 'restart', 'start', 'stop'
    ];

    // Deployment keywords
    const deploymentKeywords = [
      'deploy', 'deployment', 'triển khai',
      'rollback', 'quay lại', 'phiên bản cũ',
      'release', 'phát hành', 'version',
      'build', 'xây dựng', 'biên dịch',
      'production', 'staging', 'môi trường'
    ];

    // Server management keywords
    const serverKeywords = [
      'server', 'máy chủ', 'host',
      'kết nối', 'connection', 'test',
      'thống kê', 'statistics', 'monitor',
      'status', 'trạng thái', 'health'
    ];

    let score = 0;

    // Tính điểm dựa trên keywords
    for (const keyword of sshKeywords) {
      if (normalizedMessage.includes(keyword)) {
        score += 0.3;
      }
    }

    for (const keyword of deploymentKeywords) {
      if (normalizedMessage.includes(keyword)) {
        score += 0.4;
      }
    }

    for (const keyword of serverKeywords) {
      if (normalizedMessage.includes(keyword)) {
        score += 0.3;
      }
    }

    // Bonus cho intent category
    if (context?.intent?.category === IntentCategory.PROJECT) {
      score += 0.2;
    }

    // Bonus cho specific actions
    if (normalizedMessage.includes('thực thi') || normalizedMessage.includes('execute')) {
      score += 0.3;
    }

    if (normalizedMessage.includes('deploy') || normalizedMessage.includes('triển khai')) {
      score += 0.4;
    }

    // Penalty nếu không liên quan đến SSH/deployment
    const irrelevantKeywords = [
      'email', 'nhân viên', 'employee', 'user management',
      'todo', 'task', 'công việc', 'calendar',
      'document', 'tài liệu', 'file upload'
    ];

    for (const keyword of irrelevantKeywords) {
      if (normalizedMessage.includes(keyword)) {
        score -= 0.2;
      }
    }

    // Đảm bảo score trong khoảng [0, 1]
    return Math.max(0, Math.min(1, score));
  }

  /**
   * Xử lý request
   */
  async process(
    message: string,
    context: any,
    config: AgentConfig,
  ): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      if (!this.agent) {
        await this.initializeAgent();
      }

      // Chuẩn bị context cho agent
      const agentConfig = {
        configurable: {
          thread_id: config.sessionId || 'default',
          userId: config.userId,
          tenantId: config.tenantId,
        },
      };

      // Thực thi với agent
      const response = await this.agent.invoke(
        { messages: [new HumanMessage(message)] },
        agentConfig,
      );

      const lastMessage = response.messages[response.messages.length - 1];
      const result = lastMessage.content;

      this.logger.log(`SSH Agent xử lý thành công: ${message.substring(0, 100)}...`);

      return {
        result,
        confidence: 0.9,
        metadata: {
          agentName: this.name,
          executionTime: Date.now() - startTime,
          toolsUsed: this.extractToolsUsed(response),
          messageCount: response.messages.length,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi trong SSH Agent: ${error.message}`, error.stack);

      return {
        result: `Xin lỗi, đã có lỗi xảy ra khi xử lý yêu cầu SSH: ${error.message}`,
        confidence: 0,
        error: error.message,
        metadata: {
          agentName: this.name,
          executionTime: Date.now() - startTime,
          error: true,
        },
      };
    }
  }

  /**
   * Trích xuất tools đã sử dụng từ response
   */
  private extractToolsUsed(response: any): string[] {
    const toolsUsed: string[] = [];
    
    if (response.messages) {
      for (const message of response.messages) {
        if (message.tool_calls) {
          for (const toolCall of message.tool_calls) {
            if (toolCall.name && !toolsUsed.includes(toolCall.name)) {
              toolsUsed.push(toolCall.name);
            }
          }
        }
      }
    }

    return toolsUsed;
  }

  /**
   * Lấy thông tin về agent
   */
  getInfo() {
    return {
      name: this.name,
      description: this.description,
      priority: this.priority,
      capabilities: [
        'SSH Server Management',
        'Command Execution',
        'Deployment Automation',
        'Rollback Management',
        'Server Monitoring',
        'Statistics & Analytics',
      ],
      supportedActions: [
        'create_ssh_server',
        'test_ssh_connection',
        'execute_ssh_command',
        'execute_batch_commands',
        'deploy_project',
        'rollback_deployment',
        'get_deployment_history',
        'get_ssh_statistics',
      ],
      toolsCount: this.getTools().length,
    };
  }

  /**
   * Reset memory của agent
   */
  async resetMemory(sessionId?: string) {
    try {
      if (sessionId) {
        // Reset specific session
        await this.memory.delete({ configurable: { thread_id: sessionId } });
      } else {
        // Reset all memory
        this.memory = new MemorySaver();
        await this.initializeAgent();
      }
      this.logger.log(`Đã reset memory SSH Agent${sessionId ? ` cho session ${sessionId}` : ''}`);
    } catch (error) {
      this.logger.error(`Lỗi reset memory SSH Agent: ${error.message}`);
    }
  }
}
